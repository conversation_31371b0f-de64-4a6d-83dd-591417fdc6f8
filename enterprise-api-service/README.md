本系统应用技术：

1. HTTP请求客户端：
   - okhttp
   - Apache HttpClient (主要在HttpConnectionUtils.java中封装使用)

2. 数据源: druid

3. 持久化插件：mybatis-plus

4. 参数校验：自定义注解

5. 接口文档：swagger

6. 多数据源：com.baomidou的dynamic-datasource

7. 时间处理: joda-time

## HTTP工具类说明

系统中HTTP请求主要通过HttpConnectionUtils工具类处理，该类提供了多种HTTP请求方法：

- `post`: 标准POST请求，设置Content-Type和apiKey请求头
- `httpPost`: 简化版POST请求，支持HTTP和HTTPS
- `getRequest`: GET请求，支持超时设置
- `postRequest`: 标准POST请求，支持超时设置