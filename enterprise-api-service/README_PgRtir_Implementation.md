# PG RTIR 第三方API同步功能实现

## 概述

本次实现为PgRtirController添加了一个新的接口，用于调用第三方API获取PG产品数据并同步到本地数据库的t_pg_rtir_md表中。

## 实现内容

### 1. 新增DTO类

#### PgRtirApiRequestDTO.java
- 第三方API请求参数DTO
- 包含分页参数（current, pageSize）和筛选条件（productNameCn, brandCode, categoryCode）

#### PgRtirApiResponseDTO.java
- 第三方API响应数据DTO
- 包含响应状态和分页数据结构

#### PgRtirApiRecordDTO.java
- API返回的单条记录DTO
- 映射第三方API返回的产品数据结构

### 2. 修改实体类

#### PgRtirMd.java
- 添加了`fpcImageUrls`字段用于存储图片URL列表（JSON格式）
- 添加了`createTime`和`updateTime`字段用于记录数据的创建和更新时间

#### PgRtirMdDTO.java
- 同步添加了`fpcImageUrls`字段

### 3. 扩展Service层

#### PgRtirService.java
新增方法：
- `syncAllDataFromThirdPartyApi()`: 定时同步所有数据的主方法，自动分页获取
- `syncDataFromThirdPartyApi()`: 单页同步方法（保留用于其他场景）
- `convertApiRecordsToEntities()`: 将API记录转换为实体对象
- `batchSaveOrUpdateData()`: 批量保存或更新数据

### 4. 扩展Controller层

#### PgRtirController.java
新增接口：
- `GET /biResult/rtir/syncDataFromApi`: 定时同步数据接口（无参数）
- 专为定时器调用设计，自动获取所有数据

### 5. 数据库变更

#### SQL脚本
- `add_fpc_image_urls_column.sql`: 为t_pg_rtir_md表添加fpc_image_urls字段

## 技术特点

### 1. 数据转换
- 自动将第三方API的数据结构转换为本地实体对象
- 处理数据类型转换（如Double转String）
- JSON数组转换为JSON字符串存储

### 2. 事务处理
- 使用`@Transactional`注解确保数据一致性
- 异常时自动回滚

### 3. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 友好的错误提示

### 4. API鉴权
- 使用与magic-api项目相同的鉴权方式
- API密钥：trax-image-recognition-91582
- 密钥：kLFRrsQxTAKt
- 签名算法：ApimSignUtil.sign()
- 请求头包含Ocp-Apim-Subscription-Key

### 4. 数据更新策略
- 基于GTIN编码判断是否为新增或更新
- 更新时保持原创建时间，更新修改时间

### 5. 自动分页机制
- 接口内部自动分页获取所有数据，每页50条
- 从第1页开始逐页获取，直到没有更多数据
- 每次API调用间隔1秒，避免对第三方服务造成压力

## 接口使用

### 定时器调用示例
```bash
# GET请求，无需参数
curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi"
```

### 响应示例
```json
{
    "code": 200,
    "success": true,
    "data": "定时同步完成，共处理15页数据，获取750条记录，成功保存750条",
    "msg": "操作成功"
}
```

### 定时器配置示例
```bash
# crontab配置：每天凌晨2点执行
0 2 * * * curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi"
```

## 第三方API信息

- **URL**: `https://qa-internal-api-b2b.cn-pgcloud.com/api/ghr-image-recognition/v1/gh/optimus/fpc/gtin/data/page`
- **方法**: POST
- **格式**: JSON

## 部署前准备

### 1. 数据库变更
执行SQL脚本添加新字段：
```sql
ALTER TABLE t_pg_rtir_md 
ADD COLUMN fpc_image_urls TEXT COMMENT 'FPC图片URL列表（JSON格式存储）';
```

### 2. 依赖检查
确保以下依赖可用：
- OkHttp（HTTP客户端）
- Jackson（JSON处理）
- MyBatis-Plus（数据库操作）

## 注意事项

1. **网络连接**: 确保服务器能够访问第三方API地址
2. **数据量**: 建议分批同步，避免一次性处理过多数据
3. **错误监控**: 建议添加监控告警，及时发现同步异常
4. **数据备份**: 同步前建议备份现有数据

## 测试

提供了测试类`PgRtirControllerTest.java`用于验证：
- 请求参数序列化/反序列化
- API响应数据解析

## 文档

详细的API文档请参考：`docs/PgRtir_API_Documentation.md`

## 后续优化建议

1. 添加重试机制处理网络异常
2. 实现增量同步，避免重复处理
3. 添加同步状态记录和监控
4. 考虑异步处理大批量数据
5. 添加数据校验规则
