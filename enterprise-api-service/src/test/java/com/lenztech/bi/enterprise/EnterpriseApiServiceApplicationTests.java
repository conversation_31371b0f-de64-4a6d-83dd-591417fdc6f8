package com.lenztech.bi.enterprise;

import com.alibaba.fastjson.JSONObject;
import com.lenztech.bi.enterprise.dto.car.CarData;
import com.lenztech.bi.enterprise.dto.car.CarDemoConstant;
import com.lenztech.bi.enterprise.dto.car.CarDetailData;
import com.lenztech.bi.enterprise.dto.car.LineChartData;
import com.lenztech.bi.enterprise.dto.car.NewPeopleData;
import com.lenztech.bi.enterprise.dto.car.OldPeopleData;
import com.lenztech.bi.enterprise.utils.ExcelUtils;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class EnterpriseApiServiceApplicationTests {

	@Test
	public void importFile() throws IOException {
		/*List<LineChartData> provinceData = ExcelUtils.importExcel("C:/Users/<USER>/Desktop/1.xlsx", 0, 1, LineChartData.class);
		String json = JsonUtil.toJsonString(provinceData);
		System.out.println("<================  开始  =================>");
		System.out.println(json);
		System.out.println("<================  结束  =================>");*/
		/*String  a = "[{\"area\":\"201 North\",\"date\":\"周一\",\"peopleNum\":240,\"peopleCodeNum\":498,\"scanCodeNum\":65,\"responseTranslateNum\":11,\"fission\":2,\"postCodeNum\":8,\"other\":0,\"ipzshopping\":1}]";
		List<LineChartData> lineChartData =  JSONObject.parseArray(a, LineChartData.class);*/
		List<CarData> carDataList = JSONObject.parseArray(CarDemoConstant.CAR_DATA, CarData.class);
		List<String>  areaList =  carDataList.stream().map(CarData::getArea).distinct().collect(Collectors.toList());
		System.out.println("<================  area  =================>");
		System.out.println(JSONObject.toJSONString(areaList));
		List<String>  cityList =  carDataList.stream().map(CarData::getCity).distinct().collect(Collectors.toList());
		System.out.println("<================  city  =================>");
		System.out.println(JSONObject.toJSONString(cityList));
		List<String>  brandList =  carDataList.stream().map(CarData::getBrand).distinct().collect(Collectors.toList());
		System.out.println("<================  brand  =================>");
		System.out.println(JSONObject.toJSONString(brandList));
		List<String>  modelList =  carDataList.stream().map(CarData::getModel).distinct().collect(Collectors.toList());
		System.out.println("<================  mdoel  =================>");
		System.out.println(JSONObject.toJSONString(modelList));
	}

}

