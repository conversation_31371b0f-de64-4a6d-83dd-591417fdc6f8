package com.lenztech.bi.enterprise;


import com.lenztech.bi.enterprise.dto.redis.SendRedisMessageDTO;
import com.lenztech.bi.enterprise.dto.redis.SendRedisProductDTO;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestRedis {

    //在MyRedisConfig文件中配置了redisTemplate的序列化之后， 客户端也能正确显示键值对了
    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${spring.redis.queue.key}")
    private String redisKey;

    @Test
    public void test() {

        ListOperations<String, String> listOperations = redisTemplate.opsForList();

//      往列表中添加元素

//        listOperations.leftPush(redisKey, JsonUtil.toJsonString(sendRedisMessageDTO));


        System.out.println(listOperations.size(redisKey));

        List<String> stringList = listOperations.range(redisKey, 0, -1);

        System.out.println(stringList.size());
    }
}
