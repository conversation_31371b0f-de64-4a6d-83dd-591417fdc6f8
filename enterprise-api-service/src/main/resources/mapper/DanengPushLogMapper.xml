<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.DanengPushLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.DanengPushLog">
        <id column="id" property="id" />
        <result column="store_code" property="storeCode" />
        <result column="response_id" property="responseId" />
        <result column="request_body" property="requestBody" />
        <result column="response_body" property="responseBody" />
        <result column="status" property="status" />
        <result column="status_code" property="statusCode" />
        <result column="error_message" property="errorMessage" />
        <result column="push_time" property="pushTime" />
        <result column="response_time" property="responseTime" />
        <result column="nonce" property="nonce" />
        <result column="customer_type" property="customerType" />
        <result column="month" property="month" />
        <result column="inspect_date" property="inspectDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 批量插入日志记录 (参考PgDpbReportMapper的实现) -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO daneng_push_log (
            store_code, response_id, request_body, response_body, 
            status, status_code, error_message, push_time, 
            response_time, nonce, customer_type, month, 
            inspect_date, create_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.storeCode}, #{item.responseId}, #{item.requestBody}, #{item.responseBody}, 
            #{item.status}, #{item.statusCode}, #{item.errorMessage}, #{item.pushTime}, 
            #{item.responseTime}, #{item.nonce}, #{item.customerType}, #{item.month}, 
            #{item.inspectDate}, #{item.createTime}
            )
        </foreach>
    </insert>

</mapper> 