<!DOCTYPE html>
<html  xmlns:th="http://www.thymeleaf.org"
       xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head th:include="layout :: htmlhead" th:with="title='用户管理'"></head>
<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!--头-->
    <div th:replace="fragments/head :: header"></div>

    <div class="layui-body" style="margin: 1%">

        <form id="userSearch" class="layui-form layui-form-pane" method="post" action="" style="margin-top: 20px;">
            <div class="layui-form-item">
                <label class="layui-form-label">用户名</label>
                <div class="layui-input-inline">
                    <input id="sysUserName" name="sysUserName" autocomplete="off" class="layui-input" type="text"/>
                </div>
                <label class="layui-form-label">手机号</label>
                <div class="layui-input-inline">
                    <input id="userPhone" name="userPhone" autocomplete="off" class="layui-input" type="text" onkeyup="this.value=this.value.replace(/\D/g,'')"
                           onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">注册时间</label>
                        <div class="layui-input-inline" style="width: 175px;">
                            <input  name="startTime" id="startTime" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input" type="text"/>
                        </div>
                    <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 175px;">
                            <input name="endTime" id="endTime" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input" type="text"/>
                        </div>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <button class="layui-btn"  lay-submit="" lay-filter="searchSubmit">提交</button>
                </div>
            </div>
        </form>

        <!--<hr class="layui-bg-blue"/>-->
        <hr class="layui-bg-black"/>

        <button class="layui-btn layui-btn-normal" onclick="addUser()">开通用户</button>

        <div style="display:none;" id="currentUser"><shiro:principal property="id"></shiro:principal></div>

        <table id="uesrList" lay-filter="userTable"></table>

        <script type="text/html" id="optBar">
            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
            {{#  if(d.userStatus == '0'){ }}
            <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="recover">恢复</a>
            {{#  } else { }}
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
            {{#  } }}
        </script>
    </div>


    <!--添加或编辑用户-->
    <div id="setUser" class="layer_self_wrap" style="width:500px;display:none;">
        <form id="userForm" class="layui-form layui-form-pane" method="post" action="" style="margin-top: 20px;">
            <input id="pageNum" type="hidden" name="pageNum"/>
            <input id="id" type="hidden" name="id"/>
            <div class="layui-form-item">
                <label class="layui-form-label">用户名</label>
                <div class="layui-input-inline">
                    <input id="username" name="sysUserName" lay-verify="required" autocomplete="off" class="layui-input" type="text"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">角色类型</label>
                <div class="layui-input-inline">
                    <select name="roleId"  id="roleId" >
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">手机号</label>
                <div class="layui-input-inline">
                    <input id="mobile" name="userPhone" lay-verify="required" autocomplete="off" class="layui-input" type="text" onkeyup="this.value=this.value.replace(/\D/g,'')"
                           onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">密码</label>
                <div class="layui-input-inline">
                    <input id="password" name="sysUserPwd" autocomplete="off" class="layui-input" type="password" placeholder="默认初始密码：123456" />
                </div>
                <div class="layui-form-mid layui-word-aux">请填写6位以上的密码</div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 10px;">
                    <button class="layui-btn"  lay-submit="" lay-filter="userSubmit">提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>

    <!--底部-->
    <div th:replace="fragments/footer :: footer"></div>
    <script src="/js/dateUtils.js"></script>
    <script src="/js/sysUser/userList.js"></script>

</div>
</body>
</html>