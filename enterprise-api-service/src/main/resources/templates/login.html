<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<!--head部分-->
<head th:include="layout :: htmlhead" th:with="title='朗镜科技有限责任公司对外服务管理系统'">
</head>
<style>
    body{
        background-repeat: no-repeat;
        background-size:100% 100%;
        background-attachment: fixed;
    }
</style>
<body background="images\bg.jpg" >
<div class="layui-container" style="height: 100%">

    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 6%">
        <legend style="margin-left: 40%"><span style="font-weight: bold;font-size:22px;">朗镜科技有限责任公司对外服务管理系统</span></legend>
    </fieldset>

    <div class="layui-row">
        <div class="layui-col-xs3">
            <div class="grid-demo grid-demo-bg1">
                &nbsp;
            </div>
        </div>
        <div class="layui-col-xs6">
            <div class="grid-demo">
                <form id="useLogin" class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-inline">
                            <input id="username" name="username" lay-verify="required" autocomplete="off" class="layui-input" type="tel"/>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">密&nbsp;&nbsp;码</label>
                        <div class="layui-input-inline">
                            <input id="password" name="password" lay-verify="required" autocomplete="off" class="layui-input" type="password"/>
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-top: -20px;">
                        <label class="layui-form-label"></label>
                        <div class="layui-inline">
                            <input type="checkbox" name="rememberMe" lay-skin="primary"  title="记住我"/>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" type="reset">重置</button>
                            <button id="loginBtn" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="login">登录</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/js/login.js"></script>
</body>
</html>