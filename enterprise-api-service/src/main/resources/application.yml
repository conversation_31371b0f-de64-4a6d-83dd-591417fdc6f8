#dev env
#spring:
#  profiles:
#    active: dev
#  application:
#    name: bi-service
#  autoconfigure:
#    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
#  #Spring boot
#  #http://docs.spring.io/spring-boot/docs/current-SNAPSHOT/reference/htmlsingle/#how-to-enable-http-response-compression
#server:
#  compression:
#    enabled: true
#    min-response-size: 10
#    excluded-user-agents: gozilla,traviata
#    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
#mybatis-plus:
#  mapper-locations: classpath:mybatis/mappers/**/*.xml
#  type-aliases-package: com.lenztech.bi.enterprise.entity

