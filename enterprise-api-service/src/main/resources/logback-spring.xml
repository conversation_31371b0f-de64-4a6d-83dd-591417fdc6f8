<?xml version="1.0"?>
<configuration debug="true" scan="true" scanPeriod="10 seconds">
    <contextName>nacos</contextName>
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path"></springProperty>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"></springProperty>
    <property name="LOG_PATH" value="${LOG_PATH:-./data/logs}"/>
    <!-- console -->
    <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%level][%r][%X{traceId},%X{spanId}][%thread][%-40.40logger][%M\(%line\)]%msg%n</Pattern>
        </encoder>
    </appender>

    <!-- file -->
    <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/${APP_NAME}/info.log</File>
        <!--  日志级别filter,只打印debug以上的日志    -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_PATH}/${APP_NAME}/info.%d{yyyy-MM-dd-HH}.log</FileNamePattern>
            <!--     保留七天       -->
            <maxHistory>168</maxHistory>
        </rollingPolicy>
        <encoder>
            <Pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%level][%r][%X{traceId},%X{spanId}][%thread][%-40.40logger][%M\(%line\)]%msg%n</Pattern>
        </encoder>
    </appender>

    <logger level="ERROR" name="com.alibaba.nacos.client.naming"></logger>
    <logger level="ERROR" name="com.alibaba.cloud.sentinel.datasource.converter"></logger>

    <!--  控制全局日志级别   -->
    <root level="INFO" additivity="false">
        <appender-ref ref="consoleAppender"/>
        <appender-ref ref="fileAppender"/>
    </root>

</configuration>
