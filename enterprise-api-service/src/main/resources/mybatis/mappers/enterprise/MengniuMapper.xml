<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.enterprise.MengniuMapper">

    <!-- 查询图片集合 -->
    <select id="getImageList" resultType="com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultImage">
        SELECT id,
               response_id,
               group_no,
               image_id,
               image_url,
               image_type,
               rec_image_url,
               status,
               status_score,
               status_desc,
               mn_sku_count,
               other_sku_count,
               mn_sku_percent,
               freezer_proportion,
               create_time
        from mengniu_image_detail
        WHERE response_id = #{responseId, jdbcType=VARCHAR}
    </select>

    <!-- 查询商品集合 -->
    <select id="getSkuList" resultType="com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSku">
        SELECT id,
               response_id,
               image_id,
               sku_code,
               sku_name,
               mn_sku_proportion,
               top,
               `left`,
               height,
               width,
               create_time
        from mengniu_image_product_detail
        WHERE response_id = #{responseId, jdbcType=VARCHAR}
    </select>

    <!-- 查询蒙牛汇总结果 -->
    <select id="getSummaryResult" resultType="com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSummary">
        SELECT id,
               response_id,
               mn_distribution_total,
               mn_facing_total,
               all_facing_total,
               mn_sku_percent,
               create_time
        from mengniu_image_h5_summary
        WHERE response_id = #{responseId, jdbcType=VARCHAR}
    </select>

    <!-- 查询蒙牛汇总sku集合 -->
    <select id="getSummarySkuList" resultType="com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiResultSummarySku">
        SELECT id,
               response_id,
               sku_code,
               sku_name,
               distribution,
               facing_count,
               create_time
        from mengniu_image_h5_summary_detail
        WHERE response_id = #{responseId, jdbcType=VARCHAR}
    </select>

</mapper>