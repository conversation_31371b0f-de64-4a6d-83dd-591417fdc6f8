<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.UnileverRolloutOsaMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.UnileverRolloutOsa">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="response_id" jdbcType="VARCHAR" property="responseId" />
    <result column="cotc_category_name" jdbcType="VARCHAR" property="cotcCategoryName" />
    <result column="cotc_category_detail" jdbcType="VARCHAR" property="cotcCategoryDetail" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="cotc_code" jdbcType="VARCHAR" property="cotcCode" />
    <result column="cotc_sku_description" jdbcType="VARCHAR" property="cotcSkuDescription" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_name_en" jdbcType="VARCHAR" property="categoryNameEn" />
    <result column="availability" jdbcType="VARCHAR" property="availability" />
  </resultMap>
  <sql id="Base_Column_List">
    id, response_id, cotc_category_name, cotc_category_detail, brand, cotc_code, cotc_sku_description, 
    category_code, category_name, category_name_en, availability
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unilever_rollout_osa
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unilever_rollout_osa
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutOsa">
    insert into unilever_rollout_osa (id, response_id, cotc_category_name, 
      cotc_category_detail, brand, cotc_code, 
      cotc_sku_description, category_code, category_name, 
      category_name_en, availability)
    values (#{id,jdbcType=INTEGER}, #{responseId,jdbcType=VARCHAR}, #{cotcCategoryName,jdbcType=VARCHAR}, 
      #{cotcCategoryDetail,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{cotcCode,jdbcType=VARCHAR}, 
      #{cotcSkuDescription,jdbcType=VARCHAR}, #{categoryCode,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, 
      #{categoryNameEn,jdbcType=VARCHAR}, #{availability,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutOsa">
    insert into unilever_rollout_osa
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="responseId != null">
        response_id,
      </if>
      <if test="cotcCategoryName != null">
        cotc_category_name,
      </if>
      <if test="cotcCategoryDetail != null">
        cotc_category_detail,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="cotcCode != null">
        cotc_code,
      </if>
      <if test="cotcSkuDescription != null">
        cotc_sku_description,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="categoryNameEn != null">
        category_name_en,
      </if>
      <if test="availability != null">
        availability,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="responseId != null">
        #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="cotcCategoryName != null">
        #{cotcCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="cotcCategoryDetail != null">
        #{cotcCategoryDetail,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="cotcCode != null">
        #{cotcCode,jdbcType=VARCHAR},
      </if>
      <if test="cotcSkuDescription != null">
        #{cotcSkuDescription,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameEn != null">
        #{categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="availability != null">
        #{availability,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutOsa">
    update unilever_rollout_osa
    <set>
      <if test="responseId != null">
        response_id = #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="cotcCategoryName != null">
        cotc_category_name = #{cotcCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="cotcCategoryDetail != null">
        cotc_category_detail = #{cotcCategoryDetail,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="cotcCode != null">
        cotc_code = #{cotcCode,jdbcType=VARCHAR},
      </if>
      <if test="cotcSkuDescription != null">
        cotc_sku_description = #{cotcSkuDescription,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameEn != null">
        category_name_en = #{categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="availability != null">
        availability = #{availability,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutOsa">
    update unilever_rollout_osa
    set response_id = #{responseId,jdbcType=VARCHAR},
      cotc_category_name = #{cotcCategoryName,jdbcType=VARCHAR},
      cotc_category_detail = #{cotcCategoryDetail,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      cotc_code = #{cotcCode,jdbcType=VARCHAR},
      cotc_sku_description = #{cotcSkuDescription,jdbcType=VARCHAR},
      category_code = #{categoryCode,jdbcType=VARCHAR},
      category_name = #{categoryName,jdbcType=VARCHAR},
      category_name_en = #{categoryNameEn,jdbcType=VARCHAR},
      availability = #{availability,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getByResponseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from unilever_rollout_osa
    where response_id = #{responseId,jdbcType=VARCHAR}
  </select>
</mapper>