<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.JmlReportMapper">

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetList" resultType="com.lenztech.bi.enterprise.dto.jml.JmlBiTarget">
		SELECT
			index_name indexName,
			sfa_product_name value
		from jinmailang_water_bi_report
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetListV2" resultType="com.lenztech.bi.enterprise.dto.jml.JmlBiTarget">
		SELECT
			index_name indexName,
			value value
		from jinmailang2_water_bi_report${shardingMonth}
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

</mapper>