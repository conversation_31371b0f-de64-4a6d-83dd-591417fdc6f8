<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.UnileverRolloutTdpDetailMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.UnileverRolloutTdpDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="response_id" jdbcType="VARCHAR" property="responseId" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="category_name_cn" jdbcType="VARCHAR" property="categoryNameCn" />
    <result column="category_name_en" jdbcType="VARCHAR" property="categoryNameEn" />
    <result column="sub_category_code" jdbcType="VARCHAR" property="subCategoryCode" />
    <result column="sub_category_name" jdbcType="VARCHAR" property="subCategoryName" />
    <result column="sub_category_name_en" jdbcType="VARCHAR" property="subCategoryNameEn" />
    <result column="ul_code" jdbcType="VARCHAR" property="ulCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="if_ps" jdbcType="VARCHAR" property="ifPs" />
    <result column="url" jdbcType="VARCHAR" property="url" />
  </resultMap>
  <sql id="Base_Column_List">
    id, response_id, category_code, category_name_cn, category_name_en, sub_category_code, 
    sub_category_name, sub_category_name_en, ul_code, bar_code, sku_name, if_ps, url
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unilever_rollout_tdp_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unilever_rollout_tdp_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutTdpDetail">
    insert into unilever_rollout_tdp_detail (id, response_id, category_code, 
      category_name_cn, category_name_en, sub_category_code, 
      sub_category_name, sub_category_name_en, ul_code, 
      bar_code, sku_name, if_ps
      )
    values (#{id,jdbcType=INTEGER}, #{responseId,jdbcType=VARCHAR}, #{categoryCode,jdbcType=VARCHAR}, 
      #{categoryNameCn,jdbcType=VARCHAR}, #{categoryNameEn,jdbcType=VARCHAR}, #{subCategoryCode,jdbcType=VARCHAR}, 
      #{subCategoryName,jdbcType=VARCHAR}, #{subCategoryNameEn,jdbcType=VARCHAR}, #{ulCode,jdbcType=VARCHAR}, 
      #{barCode,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{ifPs,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutTdpDetail">
    insert into unilever_rollout_tdp_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="responseId != null">
        response_id,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="categoryNameCn != null">
        category_name_cn,
      </if>
      <if test="categoryNameEn != null">
        category_name_en,
      </if>
      <if test="subCategoryCode != null">
        sub_category_code,
      </if>
      <if test="subCategoryName != null">
        sub_category_name,
      </if>
      <if test="subCategoryNameEn != null">
        sub_category_name_en,
      </if>
      <if test="ulCode != null">
        ul_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="ifPs != null">
        if_ps,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="responseId != null">
        #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameCn != null">
        #{categoryNameCn,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameEn != null">
        #{categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryCode != null">
        #{subCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryNameEn != null">
        #{subCategoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="ulCode != null">
        #{ulCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="ifPs != null">
        #{ifPs,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutTdpDetail">
    update unilever_rollout_tdp_detail
    <set>
      <if test="responseId != null">
        response_id = #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameCn != null">
        category_name_cn = #{categoryNameCn,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameEn != null">
        category_name_en = #{categoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryCode != null">
        sub_category_code = #{subCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryName != null">
        sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="subCategoryNameEn != null">
        sub_category_name_en = #{subCategoryNameEn,jdbcType=VARCHAR},
      </if>
      <if test="ulCode != null">
        ul_code = #{ulCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="ifPs != null">
        if_ps = #{ifPs,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutTdpDetail">
    update unilever_rollout_tdp_detail
    set response_id = #{responseId,jdbcType=VARCHAR},
      category_code = #{categoryCode,jdbcType=VARCHAR},
      category_name_cn = #{categoryNameCn,jdbcType=VARCHAR},
      category_name_en = #{categoryNameEn,jdbcType=VARCHAR},
      sub_category_code = #{subCategoryCode,jdbcType=VARCHAR},
      sub_category_name = #{subCategoryName,jdbcType=VARCHAR},
      sub_category_name_en = #{subCategoryNameEn,jdbcType=VARCHAR},
      ul_code = #{ulCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      if_ps = #{ifPs,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getByResponseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from unilever_rollout_tdp_detail
    where response_id = #{responseId,jdbcType=VARCHAR}
  </select>
</mapper>