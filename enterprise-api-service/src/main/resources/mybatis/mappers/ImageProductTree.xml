<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.ImageProductTreeMapper" >


    <!-- 根据rid查询Tree表 -->
    <select id="getImageProductTreeByResponseId" resultType="com.lenztech.bi.enterprise.entity.ImageProductTree">
        SELECT
            tree.id,
            tree.store_id storeId
        FROM
            t_image_product_tree tree
        LEFT JOIN t_image_product pro ON tree.id = pro.product_id
        WHERE
            pro.response_id = #{responseId, jdbcType=VARCHAR}
    </select>


    <!-- 根据产品id查询显示产品名称查询 -->
    <select id="getDisplayProductByProductIdList" resultType="com.lenztech.bi.enterprise.entity.DisplayProduct">
        SELECT
            qingpi.display_id displayId,
            qingpi.product_id productId,
            tree.name name
        FROM
            lenzbi.qingpi_tar_product_list qingpi
        LEFT JOIN t_image_product_tree tree ON qingpi.display_id = tree.id
        WHERE
            qingpi.product_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 通过父节点id获取子节点id集合 -->
    <select id="getImageProductTreeIdListByParentId" resultType="java.lang.String">
        SELECT
            Id
        FROM
            t_image_product_tree
        WHERE
            parent_id = #{parentId, jdbcType=VARCHAR}
    </select>

    <!-- 通过任务Id获取设置的sku信息 -->
    <select id="getImageProductTreeListByTaskId" resultType="com.lenztech.bi.enterprise.entity.ImageProductTree">
        SELECT
            tree.id id,
            tree.name name,
            tree.e_name eName,
            tree.image_url imageUrl,
            tree.type type,
            tree.store_id storeId,
            tarpro.taskid taskId
        FROM
            t_image_product_tree tree
        LEFT JOIN t_image_task_target_product tarpro ON tree.id = tarpro.product_id
        WHERE
            tarpro.taskid = #{taskId, jdbcType=VARCHAR}
        ORDER BY
            order_index
    </select>

    <select id="getImageProductTreeByProductIdList" resultType="com.lenztech.bi.enterprise.entity.ImageProductTree">
        SELECT
            tree.id,
            tree.image_url
        FROM
            t_image_product_tree tree
        WHERE
            id IN
        <foreach collection="list" item="productId" separator="," open="(" close=")">
            #{productId, jdbcType=INTEGER}
        </foreach>
    </select>

</mapper>