<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.UnileverRolloutStoreRecordMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.UnileverRolloutStoreRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="response_id" jdbcType="VARCHAR" property="responseId" />
    <result column="visit_id" jdbcType="VARCHAR" property="visitId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="url" jdbcType="VARCHAR" property="url" />
  </resultMap>
  <sql id="Base_Column_List">
    id, response_id, visit_id, start_time, end_time, store_code, store_name, channel_code,url
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unilever_rollout_store_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unilever_rollout_store_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutStoreRecord">
    insert into unilever_rollout_store_record (id, response_id, visit_id, 
      start_time, end_time, store_code, 
      store_name, channel_code)
    values (#{id,jdbcType=INTEGER}, #{responseId,jdbcType=VARCHAR}, #{visitId,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{storeCode,jdbcType=VARCHAR}, 
      #{storeName,jdbcType=VARCHAR}, #{channelCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutStoreRecord">
    insert into unilever_rollout_store_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="responseId != null">
        response_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="channelCode != null">
        channel_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="responseId != null">
        #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutStoreRecord">
    update unilever_rollout_store_record
    <set>
      <if test="responseId != null">
        response_id = #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null">
        channel_code = #{channelCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenztech.bi.enterprise.entity.UnileverRolloutStoreRecord">
    update unilever_rollout_store_record
    set response_id = #{responseId,jdbcType=VARCHAR},
      visit_id = #{visitId,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      channel_code = #{channelCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getByResponseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from unilever_rollout_store_record
    where response_id = #{responseId,jdbcType=VARCHAR}
  </select>
</mapper>