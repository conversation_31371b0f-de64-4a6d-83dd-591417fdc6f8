<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.PecReportMapper">

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetList" resultType="com.lenztech.bi.enterprise.dto.pec.PecBiTarget">
		SELECT
			image_id imageId,
			scene recogScene,
			image_type_seq section,
			layer layer,
			customer_code customerCode,
			product_type_no productTypeNo,
			number_of_rows numberOfRows,
			sku_no_seq skuNoSeq,
			sku_no_num_percent skuNoNumPercent,
			area_of_display areaOfDisplay,
			number_of_boxes_x numberOfBoxesX,
			number_of_boxes_y numberOfBoxesY,
			number_of_display_total numberOfDisplayTotal,
			brand_of_refrigerator brandOfRefrigerator,
		    layers layers,
			parent_image_type_seq parentImageTypeSeq
		 from pec_poc_1911${shardingMonth}
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

</mapper>