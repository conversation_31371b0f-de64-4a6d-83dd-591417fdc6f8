<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.QianzhenStoreDistributionMapper">


    <select id="getListDynamicTable" resultType="com.lenztech.bi.enterprise.entity.QianzhenStoreDistribution">
        SELECT id,
               response_id   as responseId,
               store_name    as storeName,
               store_num     as storeNum,
               store_address as storeAddress,
               store_type    as storeType,
               visit_time    as visitTime,
               product_id    as productId,
               product_name  as productName,
               facing,
               mdist,
               has_dist      as hasDist,
               barcode,
               create_time   as createTime,
               appeal_status as appealStatus,
               sort
        FROM ${prefix}_store_distribution
        WHERE response_id = #{responseId,jdbcType=VARCHAR};
    </select>

</mapper>
