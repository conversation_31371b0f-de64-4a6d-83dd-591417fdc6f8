<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.XuehuaResultsMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.XuehuaResults">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="response_id" jdbcType="VARCHAR" property="responseId" />
    <result column="img_id" jdbcType="VARCHAR" property="imgId" />
    <result column="if_stitch" jdbcType="INTEGER" property="ifStitch" />
    <result column="stitch_url" jdbcType="VARCHAR" property="stitchUrl" />
    <result column="remakeScore" jdbcType="REAL" property="remakescore" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="scene_no" jdbcType="VARCHAR" property="sceneNo" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_id_xh" jdbcType="VARCHAR" property="productIdXh" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="layer" jdbcType="INTEGER" property="layer" />
    <result column="facing" jdbcType="INTEGER" property="facing" />
    <result column="position" jdbcType="INTEGER" property="position" />
    <result column="count" jdbcType="INTEGER" property="count" />
  </resultMap>
  <sql id="Base_Column_List">
    id, response_id, img_id,if_stitch,stitch_url, remakeScore, group_id, scene, scene_no, product_id, product_id_xh,
    product_name, layer, facing, position, count
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from xuehua_results
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from xuehua_results
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenztech.bi.enterprise.entity.XuehuaResults">
    insert into xuehua_results (id, response_id, img_id, 
      remakeScore, group_id, scene, 
      product_id, product_id_xh, product_name, 
      layer, facing, position, 
      count)
    values (#{id,jdbcType=INTEGER}, #{responseId,jdbcType=VARCHAR}, #{imgId,jdbcType=VARCHAR}, 
      #{remakescore,jdbcType=REAL}, #{groupId,jdbcType=INTEGER}, #{scene,jdbcType=VARCHAR}, 
      #{productId,jdbcType=INTEGER}, #{productIdXh,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR}, 
      #{layer,jdbcType=INTEGER}, #{facing,jdbcType=INTEGER}, #{position,jdbcType=INTEGER}, 
      #{count,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.lenztech.bi.enterprise.entity.XuehuaResults">
    insert into xuehua_results
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="responseId != null">
        response_id,
      </if>
      <if test="imgId != null">
        img_id,
      </if>
      <if test="remakescore != null">
        remakeScore,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productIdXh != null">
        product_id_xh,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="layer != null">
        layer,
      </if>
      <if test="facing != null">
        facing,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="count != null">
        count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="responseId != null">
        #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="imgId != null">
        #{imgId,jdbcType=VARCHAR},
      </if>
      <if test="remakescore != null">
        #{remakescore,jdbcType=REAL},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="productIdXh != null">
        #{productIdXh,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="layer != null">
        #{layer,jdbcType=INTEGER},
      </if>
      <if test="facing != null">
        #{facing,jdbcType=INTEGER},
      </if>
      <if test="position != null">
        #{position,jdbcType=INTEGER},
      </if>
      <if test="count != null">
        #{count,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenztech.bi.enterprise.entity.XuehuaResults">
    update xuehua_results
    <set>
      <if test="responseId != null">
        response_id = #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="imgId != null">
        img_id = #{imgId,jdbcType=VARCHAR},
      </if>
      <if test="remakescore != null">
        remakeScore = #{remakescore,jdbcType=REAL},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="productIdXh != null">
        product_id_xh = #{productIdXh,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="layer != null">
        layer = #{layer,jdbcType=INTEGER},
      </if>
      <if test="facing != null">
        facing = #{facing,jdbcType=INTEGER},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=INTEGER},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenztech.bi.enterprise.entity.XuehuaResults">
    update xuehua_results
    set response_id = #{responseId,jdbcType=VARCHAR},
      img_id = #{imgId,jdbcType=VARCHAR},
      remakeScore = #{remakescore,jdbcType=REAL},
      group_id = #{groupId,jdbcType=INTEGER},
      scene = #{scene,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      product_id_xh = #{productIdXh,jdbcType=INTEGER},
      product_name = #{productName,jdbcType=VARCHAR},
      layer = #{layer,jdbcType=INTEGER},
      facing = #{facing,jdbcType=INTEGER},
      position = #{position,jdbcType=INTEGER},
      count = #{count,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getResultByResponseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from xuehua_results
    where response_id = #{responseId,jdbcType=VARCHAR}
  </select>
</mapper>