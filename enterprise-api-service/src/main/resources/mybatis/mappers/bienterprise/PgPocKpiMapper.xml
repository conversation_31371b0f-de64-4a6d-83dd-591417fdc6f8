<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.bienterprise.PgPocKpiMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.PgPocKpiEntity">
        <result column="taskid" property="taskId" jdbcType="VARCHAR"/>
        <result column="rid" property="rid" jdbcType="VARCHAR"/>
        <result column="facing_gold" property="facingGold" jdbcType="INTEGER"/>
        <result column="dis_must_real" property="disMustReal" jdbcType="INTEGER"/>
        <result column="dis_must_all" property="disMustAll" jdbcType="INTEGER"/>
        <result column="dis_real" property="disReal" jdbcType="INTEGER"/>
        <result column="dis_all" property="disAll" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="scene" property="scene" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        taskid,
		rid,
		facing_gold,
		dis_must_real,
		dis_must_all,
		dis_real,
		dis_all,
		update_time,
		scene
	</sql>

    <sql id="searchCondition">
        1 = 1
        <if test="responseId != null">
            AND rid = #{responseId}
        </if>
    </sql>

    <select id="selectPgPocKpiEntityByRid" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        t_poc_kpi
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>