<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.bienterprise.PgPocSkuExistMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.PgPocSkuExistEntity">
        <result column="taskid" property="taskid" jdbcType="VARCHAR"/>
        <result column="rid" property="rid" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="pid" property="pid" jdbcType="INTEGER"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="if_must" property="ifMust" jdbcType="VARCHAR"/>
        <result column="facing" property="facing" jdbcType="VARCHAR"/>
        <result column="layer" property="layer" jdbcType="VARCHAR"/>
        <result column="if_gold_layer" property="ifGoldLayer" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="columns">
        taskid,
		rid,
		brand,
		pid,
		product_name,
		if_must,
		facing,
		layer,
		if_gold_layer,
		update_time
	</sql>

    <sql id="searchCondition">
        1 = 1
        <if test="responseId != null">
            AND rid = #{responseId}
        </if>
    </sql>

    <select id="listPgPocSkuExistEntity" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        t_poc_sku_exist
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>