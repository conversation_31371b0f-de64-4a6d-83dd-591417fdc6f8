<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.FerreroReportMapper">

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetList" resultType="com.lenztech.bi.enterprise.dto.ferrero.FerreroBiReport"  >
		SELECT
			task_id,
			response_id,
			img_id,
			img_local_path imgUrl,
			scene,
			sceneInstanceNo,
			customer_id productId,
			product_name,
			facing facingCount
		FROM feilieluo_bi_report
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>


</mapper>