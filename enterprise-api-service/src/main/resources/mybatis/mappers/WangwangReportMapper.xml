<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.WangwangReportMapper">

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetList" resultType="com.lenztech.bi.enterprise.dto.wangwang.WangwangBiReport">
		SELECT
			group_id groupId,
			source_img_id_list sourceImgIdList,
			stitch_img_url stitchImgUrl,
			img_url imgUrl,
			rec_url recUrl,
			img_id imgId,
			img_height imgHeight,
			img_width imgWidth,
			shelf_height shelfHeight,
			num_layers numLayers,
			num_patchs numPatchs,
			is_remake isRemake,
			remake_score remakeScore,
			product_id productId,
			product_name productName,
			coordinate coordinate,
			patch_height patchHeight,
			patch_width patchWidth,
			layer layer,
			`column`,
			scene scene,
			if_facing ifFacing,
			facing_count facingCount,
			update_time updateTime
		FROM wangwang_bi_report aa
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>


</mapper>