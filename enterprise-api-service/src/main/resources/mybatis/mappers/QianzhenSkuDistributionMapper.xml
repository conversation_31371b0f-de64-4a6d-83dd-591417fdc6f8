<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.QianzhenSkuDistributionMapper">

    <select id="getListDynamicTable" resultType="com.lenztech.bi.enterprise.entity.QianzhenSkuDistribution">
        SELECT id,
               response_id  as responseId,
               image_id     as imageId,
               image_url    as imageUrl,
               product_id   as productId,
               product_name as productName,
               crood,
               level,
               is_remake    as isRemake,
               is_pose      as isPose
        FROM ${prefix}_sku_distribution
        WHERE response_id = #{responseId,jdbcType=VARCHAR}
        AND product_id != "-500"
    </select>

</mapper>
