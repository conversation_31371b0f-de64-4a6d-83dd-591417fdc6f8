<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.JzReportMapper">

	<!-- 查询图片集合 -->
	<select id="getImageList" resultType="com.lenztech.bi.enterprise.dto.jz.JzBiTargetImage">
		SELECT
			response_id,
			img_id imageId,
			img_url imageUrl,
			rec_url,
			height,
			width,
			shelf_height,
			num_layers,
			num_patches,
			remake_score
		from jz_img_results
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

	<!-- 查询商品集合 -->
	<select id="getProductList" resultType="com.lenztech.bi.enterprise.dto.jz.JzBiTargetProduct">
		SELECT
			response_id,
			img_id imageId,
			patch_id,
			sku_code,
			x_min,
			y_min xMax,
			x_max yMin,
			y_max,
			patch_height,
			patch_width,
			facing,
			layer,
			`column`
		from jz_patch_results
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

</mapper>