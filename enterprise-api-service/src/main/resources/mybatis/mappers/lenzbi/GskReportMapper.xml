<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.GskReportMapper">

	<!-- 根据拜访id查询 -->
	<select id="getBiTargetList" resultType="com.lenztech.bi.enterprise.dto.gsk.GskBiReport">
		SELECT
			id,
			response_id,
			group_id,
			source_img_id_list,
			stitch_image_url,
			img_url,
			rec_url,
			img_id,
			img_height imgHeight,
			img_width imgWidth,
			shelf_height shelfHeight,
			num_layers numLayers,
			num_patchs numPatchs,
			is_remake isRemake,
			remake_score remakeScore,
			product_id productId,
			coordinate coordinate,
			patch_height patchHeight,
			patch_width patchWidth,
			layer layer,
			`column`,
			scene scene,
			if_facing ifFacing,
			facing_count facingCount,
			update_time updateTime
		FROM gsk_fmcg_bi_report aa
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>


</mapper>