<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.BiResponseRecordMapper">
  <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.BiResponseRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="response_id" jdbcType="VARCHAR" property="responseId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_id, response_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bi_response_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from bi_response_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.lenztech.bi.enterprise.entity.BiResponseRecord">
    insert into bi_response_record (id, task_id, response_id, 
      update_time)
    values (#{id,jdbcType=INTEGER}, #{taskId,jdbcType=VARCHAR}, #{responseId,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.lenztech.bi.enterprise.entity.BiResponseRecord">
    insert into bi_response_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="responseId != null">
        response_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="responseId != null">
        #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.lenztech.bi.enterprise.entity.BiResponseRecord">
    update bi_response_record
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="responseId != null">
        response_id = #{responseId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.lenztech.bi.enterprise.entity.BiResponseRecord">
    update bi_response_record
    set task_id = #{taskId,jdbcType=VARCHAR},
      response_id = #{responseId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getRecordList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bi_response_record
    where response_id = #{responseId,jdbcType=VARCHAR}
  </select>
</mapper>