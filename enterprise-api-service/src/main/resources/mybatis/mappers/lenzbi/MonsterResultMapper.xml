<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.MonsterResultsMapper">


    <select id="getMonsterPosmAvailabilityAvgList"
            resultType="com.lenztech.bi.enterprise.entity.MonsterPosmAvailability">
        select fact,avg(posm_availability_2020) posmAvailability2020, avg(posm_availability_2021) posmAvailability2021 from monster_posm_availability where bg=#{bg} and period=#{period} GROUP by fact
    </select>

    <select id="getMonsterMec2ndDisplayAvgList"
            resultType="com.lenztech.bi.enterprise.entity.MonsterMec2ndDisplay">
        select avg(mec_2nd_display_2020) mec2ndDisplay2020, avg(mec_2nd_display_2021) mec2ndDisplay2021 from monster_mec_2nd_display where bg=#{bg} and period=#{period}
    </select>

    <select id="getMonsterDistributionByBrandAvgList"
            resultType="com.lenztech.bi.enterprise.entity.MonsterDistributionByBrand">
			SELECT
				avg( distribution ) distribution,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				channel,
				brand,
				period
			FROM
				monster_distribution_by_brand
			WHERE
				period=#{period}
					<if test="bg != null and bg != ''">
				and bg=#{bg}
					</if>
					<if test="city != null and city != ''">
				and city=#{city}
					</if>
			GROUP BY
				channel,
				brand
    </select>

	<select id="getMonsterDistributionByFlavorAvgList"
			resultType="com.lenztech.bi.enterprise.entity.MonsterDistributionByFlavor">
			SELECT
				avg( distribution ) distribution,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				channel,
				flavor,
				period
			FROM
				monster_distribution_by_flavor
			WHERE
					period=#{period}
					<if test="bg != null and bg != ''">
						and bg=#{bg}
					</if>
					<if test="city != null and city != ''">
						and city=#{city}
					</if>
			GROUP BY
				channel,
				flavor
	</select>

	<select id="getMonsterUtcDistributionByFlavorAvgList"
			resultType="com.lenztech.bi.enterprise.entity.MonsterUtcDistributionByFlavor">
			SELECT
				avg( distribution ) distribution,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				channel,
				flavor,
				period
			FROM
				monster_utc_distribution_by_flavor
			WHERE
					period=#{period}
					<if test="bg != null and bg != ''">
						and bg=#{bg}
					</if>
					<if test="city != null and city != ''">
						and city=#{city}
					</if>
			GROUP BY
				channel,
				flavor
	</select>

	<select id="getMonsterStoreRatioByMecFlavorNumberAvgList"
			resultType="com.lenztech.bi.enterprise.entity.MonsterStoreRatioByMecFlavorNumber">
			SELECT
				avg( average_sku_per_store ) averageSkuPerStore,
				avg( store_ratio_by_mec_flavor_number ) storeRatioByMecFlavorNumber,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				period,
				channel,
				flavor_number flavorNumber
			FROM
				monster_store_ratio_by_mec_flavor_number
			WHERE
				period=#{period}
				<if test="bg != null and bg != ''">
					and bg=#{bg}
				</if>
				<if test="city != null and city != ''">
					and city=#{city}
				</if>
			GROUP BY
				channel,
				flavor_number
	</select>

	<select id="getMonsterMecPositioningInMainShelfAvgList"
			resultType="com.lenztech.bi.enterprise.entity.MonsterMecPositioningInMainShelf">
			SELECT
				avg( mec_positioning_in_main_shelf ) mecPositioningInMainShelf,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				period,
				channel,
				fact
			FROM
				monster_mec_positioning_in_main_shelf
			WHERE
				period=#{period}
				<if test="bg != null and bg != ''">
					and bg=#{bg}
				</if>
				<if test="city != null and city != ''">
					and city=#{city}
				</if>
			GROUP BY
				channel,
				fact
	</select>

	<select id="getMonsterMecPositioningInCustomerCoolerAvgList"
			resultType="com.lenztech.bi.enterprise.entity.MonsterMecPositioningInCustomerCooler">
			SELECT
				avg( mec_positioning_in_customer_cooler ) mecPositioningInCustomerCooler,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				period,
				channel,
				fact
			FROM
				monster_mec_positioning_in_customer_cooler
			WHERE
				period=#{period}
				<if test="bg != null and bg != ''">
					and bg=#{bg}
				</if>
				<if test="city != null and city != ''">
					and city=#{city}
				</if>
			GROUP BY
				channel,
				fact
	</select>

	<select id="getMonsterSuctionRackAvailabilityAvgList"
			resultType="com.lenztech.bi.enterprise.entity.MonsterSuctionRackAvailability">
			SELECT
				avg( suction_rack_availability ) suctionRackAvailability,
				avg( vs_ya ) vsYa,
				avg( vs_pp ) vsPp,
				period,
				channel,
				cooler_type
			FROM
				monster_suction_rack_availability
			WHERE
				period=#{period}
				<if test="bg != null and bg != ''">
					and bg=#{bg}
				</if>
				<if test="city != null and city != ''">
					and city=#{city}
				</if>
			GROUP BY
				channel,
				cooler_type
	</select>


</mapper>
