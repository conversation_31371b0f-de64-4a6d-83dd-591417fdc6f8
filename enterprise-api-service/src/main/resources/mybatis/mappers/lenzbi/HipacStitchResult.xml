<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.HiPacStitchReportMapper">

    <!-- 查询图片集合 -->
    <select id="getImageList" resultType="com.lenztech.bi.enterprise.entity.HipacStitchResult">
		SELECT
			response_id responseId,
			group_id groupId,
			source_img_id_list sourceImgIdList,
			stitch_img_url stitchImgUrl,
			is_stitch_good isStitchGood
		from hipac_stitch_result
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

</mapper>