<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.HiPacPatchReportMapper">

    <!-- 查询商品集合 -->
    <select id="getProductList" resultType="com.lenztech.bi.enterprise.entity.HipacPatchResult">
		SELECT
			response_id responseId,
			img_id imgId,
			sku_code skuCode,
			sku_name skuName,
			coordinate coordinate,
			mosaic_coords mosaicCoords,
			facing_count facingCount
		from hipac_patch_result
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>


</mapper>