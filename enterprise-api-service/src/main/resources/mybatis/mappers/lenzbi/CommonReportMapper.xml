<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.CommonReportMapper">

	<!-- 查询图片集合 -->
	<select id="getImageList" resultType="com.lenztech.bi.enterprise.dto.common.CommonBiImage">
		SELECT
		    id,
			response_id,
			image_id,
			image_url,
			rec_image_url,
			height,
			width,
			remake,
			remake_score,
			product_total,
			create_time
		from response_image_detail${shardingMonth}
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

	<!-- 查询商品集合 -->
	<select id="getProductList" resultType="com.lenztech.bi.enterprise.dto.common.CommonBiProduct">
		SELECT
			id,
			response_id,
			image_id,
			product_id,
			product_name,
			isfaceing,
			layer,
			`column`,
			height,
			width,
			scene,
			coordinate,
			create_time
		from response_product_detail${shardingMonth}
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
  	</select>

	<!-- 根据responseId查询表数据集合 -->
	<select id="getTableDataList" resultType="java.util.Map">
		SELECT
			*
		from ${tableName}
		WHERE response_id = #{responseId, jdbcType=VARCHAR}
	</select>

</mapper>