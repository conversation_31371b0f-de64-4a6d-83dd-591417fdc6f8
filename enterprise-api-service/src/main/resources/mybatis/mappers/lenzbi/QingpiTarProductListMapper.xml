<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.QingpiTarProductListMapper">

	<!-- 考核SKU集合 -->
	<select id="getQingpiProductList" resultType="com.lenztech.bi.enterprise.dto.tsingtao.TsingtaoProductKpi">
        SELECT
            product_group,
            tar_dist_num,
            merge_id
        FROM product_rule_nanqu_qingpi_202006
        WHERE 1=1
			AND daqu = #{areaName}
			AND system_name = #{systemName}
    </select>

</mapper>
