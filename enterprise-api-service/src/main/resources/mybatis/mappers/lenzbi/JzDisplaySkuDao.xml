<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.JzV2DisplaySkuMapper">

    <!--查询图片集合-->
    <select id="getSceneList" resultType="com.lenztech.bi.enterprise.dto.jzv2.JzDisplaySku">
        select
            response_id responseId,
            img_id imgId,
            scene_seq sceneSeq,
            sku_code skuCode,
            sku_name skuName,
            count,
            update_time updateTime
        from jz_display_sku
        where response_id = #{responseId, jdbcType=VARCHAR}
    </select>

</mapper>