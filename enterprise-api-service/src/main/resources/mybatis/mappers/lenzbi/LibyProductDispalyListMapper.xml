<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.LibyProductDisplayListMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.LibyProductDispalyListEntity">
        <result column="product_id" property="productId" jdbcType="INTEGER"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="display_name" property="displayName" jdbcType="VARCHAR"/>
        <result column="is_check" property="isCheck" jdbcType="INTEGER"/>
        <result column="rule" property="rule" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        product_id,
		product_name,
		display_name,
		is_check,
		rule,
		category,
		brand
	</sql>

    <sql id="searchCondition">
        1 = 1
    </sql>

    <select id="listLibyProductDisplayList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        liby_product_dispaly_list
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>