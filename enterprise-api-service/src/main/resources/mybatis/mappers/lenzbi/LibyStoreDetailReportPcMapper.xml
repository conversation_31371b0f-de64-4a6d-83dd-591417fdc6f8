<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.LibyStoreDetailReportPcMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.LibyStoreDetailReportPcEntity">
        <result column="taskid" property="taskId" jdbcType="VARCHAR"/>
        <result column="response_id" property="responseId" jdbcType="VARCHAR"/>
        <result column="rule" property="rule" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="is_exist" property="isExist" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="columns">
        taskid,
		response_id,
		rule,
		product_id,
		product_name,
		is_exist,
		update_time
	</sql>

    <sql id="searchCondition">
        1 = 1
        <if test="responseId != null">
            AND response_id = #{responseId}
        </if>
    </sql>

    <select id="listLibyStoreDetailReportPc" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        liby_store_detial_report_pc
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>