<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lenztech.bi.enterprise.mapper.lenzbi.LibyStoreReportPcMapper">

    <resultMap id="BaseResultMap" type="com.lenztech.bi.enterprise.entity.LibyStoreReportPcEntity">
        <result column="taskid" property="taskId" jdbcType="VARCHAR"/>
        <result column="response_id" property="responseId" jdbcType="VARCHAR"/>
        <result column="place" property="place" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="store_code" property="storeCode" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="store_type" property="storeType" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="up_time" property="upTime" jdbcType="TIMESTAMP"/>
        <result column="total_score" property="totalScore" jdbcType="DECIMAL"/>
        <result column="liby_duixing" property="libyDuixing" jdbcType="INTEGER"/>
        <result column="liby_dui_num" property="libyDuiNum" jdbcType="INTEGER"/>
        <result column="is_exist_not_liby_display" property="isExistNotLibyDisplay" jdbcType="INTEGER"/>
        <result column="didui_defen" property="diduiDefen" jdbcType="DECIMAL"/>
        <result column="liandai_defen" property="liandaiDefen" jdbcType="DECIMAL"/>
        <result column="wuliao_defen" property="wuliaoDefen" jdbcType="DECIMAL"/>
        <result column="quge_defen" property="qugeDefen" jdbcType="DECIMAL"/>
        <result column="guatiao_defen" property="guatiaoDefen" jdbcType="DECIMAL"/>
        <result column="liby_liandai" property="libyLiandai" jdbcType="INTEGER"/>
        <result column="liby_guatiao_liandai" property="libyGuatiaoLiandai" jdbcType="INTEGER"/>
        <result column="haobaba_liandai" property="haobabaLiandai" jdbcType="INTEGER"/>
        <result column="chaowei_liandai" property="chaoweiLiandai" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="columns">
        taskid,
		response_id,
		place,
		city,
		store_code,
		store_name,
		store_type,
		start_time,
		up_time,
		total_score,
		liby_duixing,
		liby_dui_num,
		is_exist_not_liby_display,
		didui_defen,
		liandai_defen,
		wuliao_defen,
		quge_defen,
		guatiao_defen,
		liby_liandai,
		liby_guatiao_liandai,
		haobaba_liandai,
		chaowei_liandai,
		update_time
	</sql>

    <sql id="searchCondition">
        1 = 1
        <if test="city != null">
            AND city = #{city}
        </if>
        <if test="startTime != null">
            AND up_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND up_time &lt; #{endTime}
        </if>
        <if test="storeType != null">
            AND store_type = #{storeType}
        </if>
        <if test="storeCode != null">
            AND store_code = #{storeCode}
        </if>
        order by up_time desc
    </sql>

    <select id="selectLibyStoreReportPcEntity" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="columns"></include>
        FROM
        liby_store_report_pc
        WHERE
        <include refid="searchCondition" />
    </select>

</mapper>