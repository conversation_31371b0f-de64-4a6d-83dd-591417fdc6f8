package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.SnowBeerBiResultResp;
import com.lenztech.bi.enterprise.dto.SnowBeerCompleteStatusResp;
import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.service.SnowBeerService;
import com.lenztech.bi.enterprise.service.WangwangReportService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 旺旺bi指标Controller
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@RestController
@RequestMapping("/biResult/snowBeer/")
@Slf4j
public class SnowBeerBiResultController {

    @Autowired
    private SnowBeerService snowBeerService;

    @RequestMapping(value = "getResult", method = RequestMethod.GET)
    public ResponseData<SnowBeerBiResultResp> getResult(String responseId) {
        try {
            SnowBeerBiResultResp snowBeerBiResultResp = snowBeerService.getResult(responseId);
            return ResponseData.success().data(snowBeerBiResultResp);
        } catch (Exception e) {
            log.error("/getResult========", e);
        }
        return ResponseData.failure();
    }

    @RequestMapping(value = "getCompleteStatus", method = RequestMethod.GET)
    public ResponseData<SnowBeerCompleteStatusResp> getCompleteStatus(String responseId) {
        try {
            SnowBeerCompleteStatusResp resp = snowBeerService.getCompleteStatus(responseId);
            return ResponseData.success().data(resp);
        } catch (Exception e) {
            log.error("/getResult========", e);
        }
        return ResponseData.failure();
    }

}
