package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lenztech.bi.enterprise.dto.gunman.SkuResultInfo;
import com.lenztech.bi.enterprise.entity.QiangshouSkuResult;
import com.lenztech.bi.enterprise.entity.QiangshouStoreRecord;
import com.lenztech.bi.enterprise.mapper.QiangshouSkuResultMapper;
import com.lenztech.bi.enterprise.mapper.QiangshouStoreRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/03/16
 * Time: 15:38
 * 类功能: 枪手poc相关接口逻辑
 */
@Service
public class GunmanReportService {

//    @Autowired
//    private TResponseMapper responseMapper;

//    @Autowired
//    private TTasklaunchMapper tasklaunchMapper;

    @Autowired
    private QiangshouSkuResultMapper qiangshouSkuResultMapper;

    @Autowired
    private QiangshouStoreRecordMapper qiangshouStoreRecordMapper;


    /**
     * 获取枪手门店信息
     * @param responseId
     * @return
     */
    public QiangshouStoreRecord getStoreinfo(String responseId){

        LambdaQueryWrapper<QiangshouStoreRecord> qiangshouStoreRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        qiangshouStoreRecordLambdaQueryWrapper.eq(QiangshouStoreRecord::getResponseId, responseId);
        QiangshouStoreRecord qiangshouStoreRecord = qiangshouStoreRecordMapper.selectOne(qiangshouStoreRecordLambdaQueryWrapper);

        return qiangshouStoreRecord;
    }

    /**
     * 获取枪手门店sku结果
     * @param responseId
     * @return
     */
    public SkuResultInfo getSkuResult(String responseId){

        SkuResultInfo skuResultInfo = new SkuResultInfo();
        LambdaQueryWrapper<QiangshouSkuResult> qiangshouSkuResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
        qiangshouSkuResultLambdaQueryWrapper.eq(QiangshouSkuResult::getResponseId, responseId);
        List<QiangshouSkuResult> qiangshouSkuResultList = qiangshouSkuResultMapper.selectList(qiangshouSkuResultLambdaQueryWrapper);
        skuResultInfo.setSkuResultList(qiangshouSkuResultList);
        Integer totalDistCount = 0;
        Integer totalFacingCount = 0;
        for (QiangshouSkuResult qiangshouSkuResult : qiangshouSkuResultList){
            totalFacingCount = totalFacingCount + qiangshouSkuResult.getFacingCount();
            if (qiangshouSkuResult.getDist()){
                totalDistCount ++;
            }
        }
        skuResultInfo.setTotalDistCount(totalDistCount);
        skuResultInfo.setTotalFacingCount(totalFacingCount);

        return skuResultInfo;
    }

}
