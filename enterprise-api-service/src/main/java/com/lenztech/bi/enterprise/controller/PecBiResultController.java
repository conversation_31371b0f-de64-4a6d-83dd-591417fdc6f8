package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.junlebao.RepeatBiResultResp;
import com.lenztech.bi.enterprise.dto.pec.PecBiTargetResp;
import com.lenztech.bi.enterprise.service.PecReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统一bi指标Controller
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@RestController
@RequestMapping("/biResult/pec/")
public class PecBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(PecBiResultController.class);

    @Autowired
    private PecReportService pecReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<JmlBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<PecBiTargetResp> get(String responseId) {
        try {
            PecBiTargetResp pecBiTargetResp = pecReportService.getBiTargetList(responseId);
            return ResponseData.success().data(pecBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取查重结果
     *
     * @param responseId
     * @return ResponseData<JmlBiTargetResp>
     */
    @RequestMapping(value = "getPecRepeatResult", method = RequestMethod.GET)
    public ResponseData<PecBiTargetResp> getPecRepeatResult(String responseId) {
        try {
            RepeatBiResultResp resp = pecReportService.getPecRepeatResult(responseId);
            return ResponseData.success().data(resp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
