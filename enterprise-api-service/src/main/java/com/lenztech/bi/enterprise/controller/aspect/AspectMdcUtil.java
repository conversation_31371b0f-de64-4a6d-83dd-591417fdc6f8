package com.lenztech.bi.enterprise.controller.aspect;

import com.lenztech.bi.enterprise.comon.Constant;
import com.lenztech.bi.enterprise.utils.ControllerUtil;
import com.lenztech.bi.enterprise.utils.IPUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.util.Date;

/**
 * @Description: 切面工具类（mdc）
 * @Author: zhangjie
 * @Date: 3/5/20 AM10:15
 */
public class AspectMdcUtil {


    /**
     * traceId设置
     *
     * @param request 请求
     * @param method 方法名
     * @date  19/8/18 AM11:57
     * @return ResponseData
     */
    public static void beginTrackIdMdc(HttpServletRequest request, String method, DateFormat dateFormat) {
        // ip
        String ip = IPUtils.getRealIP(request);
        String traceId = "";

        if (StringUtils.isNotBlank(request.getHeader(Constant.HEADER_TRACE_ID))) {
            traceId = request.getHeader(Constant.HEADER_TRACE_ID);
        } else {
            String time = dateFormat.format(new Date());
            long id = Thread.currentThread().getId();

            traceId = method + "." + time + "." + ip + "." + id;
        }
        // traceId
        MDC.put(Constant.HEADER_TRACE_ID, traceId);
        // ip
        MDC.put(Constant.KEY_IP, request.getRemoteAddr());
    }

    /**
     * 请求Controller的路径设置
     * @param controllerBeanClass Controller类class
     * @param targetMethod Controller类方法
     */
    public static void beginUrlPathMdc(Class controllerBeanClass, Method targetMethod){
        String path = ControllerUtil.lookupRequestPath(controllerBeanClass, targetMethod);
        MDC.put(Constant.KEY_URL_PATH, path);
    }

    /**
     * mdc键值设置
     * @param key
     * @param value
     */
    public static void beginMdcWithKeyAndValue(String key, String value){
        MDC.put(key, value);
    }

    /**
     * 耗时设置
     * @param time 耗时
     */
    public static void beginConsumeTimeMdc(long time){
        MDC.put(Constant.KEY_TIMEELAPSED, String.valueOf(time));
    }
}
