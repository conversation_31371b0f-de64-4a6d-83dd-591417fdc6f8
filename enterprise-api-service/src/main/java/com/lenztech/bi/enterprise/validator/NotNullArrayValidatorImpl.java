package com.lenztech.bi.enterprise.validator;

import com.lenztech.bi.enterprise.validator.annotation.NotNull;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class NotNullArrayValidatorImpl implements ConstraintValidator<NotNull, Object[]> {


    @Override
    public void initialize(NotNull constraintAnnotation) {
    }

    @Override
    public boolean isValid(Object[] object, ConstraintValidatorContext context) {
        if(null == object || object.length == 0){
            return false;
        }
        return true;
    }
}