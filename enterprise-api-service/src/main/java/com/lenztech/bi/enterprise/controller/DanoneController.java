package com.lenztech.bi.enterprise.controller;


import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.controller.base.BaseController;
import com.lenztech.bi.enterprise.service.DanoneService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <p>
 * 主数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@RequestMapping("/biResult/danone/")
public class DanoneController extends BaseController {
    @Autowired
    DanoneService danoneService;
    ExecutorService executorService = Executors.newFixedThreadPool(1);

    public static final Logger logger = LoggerFactory.getLogger(DanoneController.class);

    /**
     * 获取并推送达能稽查结果
     *
     * @param searchDate
     * @return
     */
    @RequestMapping(value = "pushDanoneInspectBiResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取并推送达能稽查结果")
    public void getPgDcpBiResult(String searchDate, String addressIDnum) {
        try {
            Runnable rn = new Runnable() {//异步处理数据
                public void run() {
                    danoneService.processAndSendData(searchDate, addressIDnum);
                }
            };
            executorService.execute(rn);
        } catch (Exception e) {
            logger.error("/pushDanoneInspectBiResult========", e);
        }
    }

}