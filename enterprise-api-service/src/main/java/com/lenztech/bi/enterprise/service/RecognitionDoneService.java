//package com.lenztech.bi.enterprise.service;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.lenztech.bi.enterprise.comon.Constant;
//import com.lenztech.bi.enterprise.entity.TResponse;
//import com.lenztech.bi.enterprise.entity.TUser;
//import com.lenztech.bi.enterprise.mapper.task.TResponseMapper;
//import com.lenztech.bi.enterprise.mapper.task.TUserMapper;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * @Description:BI报表重构设置
// * @Author: zhangjie
// * @Date: 4/07/20 AM10:30
// */
//@Service
//public class RecognitionDoneService {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(RecognitionDoneService.class);
//
//
//    @Autowired
//    private BiComputeDataService biComputeDetailDataService;
//
//    @Autowired
//    private TResponseMapper responseMapper;
//
//    @Autowired
//    private TUserMapper userMapper;
//
//    /**
//     * 识别完成
//     * @param responseId 答卷ID
//     */
//    public void recognitionDone(String responseId){
//        // 如果是青啤项目，计算bi详情页所需的中间表数据
//        if (isQingPiProject(responseId)){
//            try {
//                biComputeDetailDataService.computeQingPiDetail(responseId);
//            } catch (Exception ex){
//                LOGGER.error("【recognitionDone】", responseId);
//            }
//        }
//    }
//
//    /**
//     * 判断是否是青啤项目
//     * @param responseId 答卷id
//     * @return
//     */
//    public boolean isQingPiProject(String responseId){
//        boolean isQingPi = false;
//
//        try {
//            LambdaQueryWrapper<TResponse> responseLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            responseLambdaQueryWrapper.eq(TResponse::getId, responseId);
//
//            TResponse response = responseMapper.selectOne(responseLambdaQueryWrapper);
//            if (response != null){
//                LambdaQueryWrapper<TUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                userLambdaQueryWrapper.eq(TUser::getId, response.getUId());
//
//                TUser user = userMapper.selectOne(userLambdaQueryWrapper);
//                if (user != null && Constant.QINGPI_FIRM.equals(user.getWhichFirm())){
//                    isQingPi = true;
//                }
//            }
//        } catch (Exception ex){
//            LOGGER.error("【isQingPiProject】", ex);
//        }
//        return isQingPi;
//    }
//}
