package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.lenztech.bi.enterprise.dto.unilever.CtaPosm;
import com.lenztech.bi.enterprise.dto.unilever.SecondDisplayImageDTO;
import com.lenztech.bi.enterprise.dto.unilever.SecondaryDisplay;
import com.lenztech.bi.enterprise.dto.unilever.SelfProductPosm;
import com.lenztech.bi.enterprise.dto.unilever.UnileverOsa;
import com.lenztech.bi.enterprise.dto.unilever.UnileverResp;
import com.lenztech.bi.enterprise.dto.unilever.UnileverTdpDetail;
import com.lenztech.bi.enterprise.entity.*;
import com.lenztech.bi.enterprise.mapper.*;
import com.lenztech.bi.enterprise.mapper.task.TResponseMapper;
import com.lenztech.bi.enterprise.mapper.task.TTasklaunchMapper;
import com.lenztech.bi.enterprise.mapper.task.UnileverSecondDisplayImageMapper;
import com.lenztech.bi.enterprise.utils.CglibCopyBeanUtil;
import com.lenztech.bi.enterprise.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2020/12/17
 * Time: 15:38
 * 类功能: 联合利华poc相关接口逻辑
 */
@Service
public class UnileverReportService {

    @Autowired
    private TResponseMapper responseMapper;

    @Autowired
    private TTasklaunchMapper tasklaunchMapper;

    @Autowired
    private UnileverStoreinfoMapper unileverStoreinfoMapper;

    @Autowired
    private UnileverCtaPosmMapper unileverCtaPosmMapper;

    @Autowired
    private UnileverOsaDetailMapper unileverOsaDetailMapper;

    @Autowired
    private UnileverOsaStatusMapper unileverOsaStatusMapper;

    @Autowired
    private UnileverProductinfoMapper unileverProductinfoMapper;

    @Autowired
    private UnileverTdpMapper unileverTdpMapper;

    @Autowired
    private UnileverSosMapper unileverSosMapper;

    @Autowired
    private UnileverPricetagMapper unileverPricetagMapper;

    @Autowired
    private UnileverStatusMapper unileverStatusMapper;

    @Autowired
    private UnileverDisplayPosmMapper unileverDisplayPosmMapper;

    @Autowired
    private UnileverSecondDisplayImageMapper unileverSecondDisplayImageMapper;

    @Autowired
    private UnileverRolloutOsaMapper unileverRolloutOsaMapper;

    @Autowired
    private UnileverRolloutStoreRecordMapper unileverRolloutStoreRecordMapper;

    @Autowired
    private UnileverRolloutTdpMapper unileverRolloutTdpMapper;

    @Autowired
    private UnileverRolloutTdpDetailMapper unileverRolloutTdpDetailMapper;

    /**
     * 还没有识别结果
     */
    private static final String NO_RESULT = "0";

    /**
     * 有识别结果 且正常识别
     */
    private static final String EXIST_PRODUCT = "1";

    /**
     * 有识别结果 但未识别出任何东西
     */
    private static final String NOT_EXIST_PRODUCT = "2";

    /**
     * 品类 织物洗护
     */
    private static final String CATEGORY_FABRIC_WASHING = "织物洗护";

    /**
     * 品类 家庭清洁
     */
    private static final String CATEGORY_HOME_CLEANING = "家庭清洁";

    /**
     * 品牌 奥妙
     */
    private static final String BRAND_SECRET = "奥妙";

    /**
     * 品牌 金纺
     */
    private static final String BRAND_GOLD_SPINNING = "金纺";

    /**
     * 品牌 花木星球
     */
    private static final String BRAND_HUAMU_PLANET = "花木星球";

    /**
     * 品牌 竞品 立白
     */
    private static final String BRAND_COMPETITOR_LIBY = "立白";

    /**
     * 品牌 竞品 雕牌
     */
    private static final String BRAND_COMPETITOR_ENGRAVING = "雕牌";

    /**
     * 场景 地堆
     */
    private static final String SCENE_PILE_OF_GROUND = "地堆";

    /**
     * 分销 是
     */
    private static final String DISTRIBUTION_YES = "Y";

    /**
     * 分销 否
     */
    private static final String DISTRIBUTION_NO = "N";

    /**
     * 获取门店相关信息
     * @param responseId
     * @return
     */
    public UnileverStoreinfo getUnileverStoreinfoByRid(String responseId){

        String addressIDnum = getAddressIDnum(responseId);
        //addressIDnum = "0021-9966";
        LambdaQueryWrapper<UnileverStoreinfo> unileverStoreinfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverStoreinfoLambdaQueryWrapper.eq(UnileverStoreinfo::getStoreCode, addressIDnum);
        UnileverStoreinfo unileverStoreinfo = unileverStoreinfoMapper.selectOne(unileverStoreinfoLambdaQueryWrapper);

        return unileverStoreinfo;
    }

    /**
     * 获取识别结果
     * @param responseId
     * @return
     */
    public String getIsRecognitionComplete(String responseId){

        LambdaQueryWrapper<UnileverStatus> unileverStatusLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverStatusLambdaQueryWrapper.eq(UnileverStatus::getResponseId, responseId);
        List<UnileverStatus> unileverOsaStatusList = unileverStatusMapper.selectList(unileverStatusLambdaQueryWrapper);
        if (unileverOsaStatusList != null && unileverOsaStatusList.size() > 0){
            return EXIST_PRODUCT;
        }else {
            return NO_RESULT;
        }
    }

    /**
     * 获取OSA分销数据
     * @param responseId
     * @return
     */
    public List<UnileverOsaStatus> getOsaDistribution(String responseId){

        LambdaQueryWrapper<UnileverOsaStatus> unileverOsaStatusLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverOsaStatusLambdaQueryWrapper.eq(UnileverOsaStatus::getResponseId, responseId);
        unileverOsaStatusLambdaQueryWrapper.isNotNull(UnileverOsaStatus::getCotcName);
        unileverOsaStatusLambdaQueryWrapper.orderByDesc(UnileverOsaStatus::getStatus).orderByAsc(UnileverOsaStatus::getCotcName);
        List<UnileverOsaStatus> unileverOsaStatusList = unileverOsaStatusMapper.selectList(unileverOsaStatusLambdaQueryWrapper);

        return unileverOsaStatusList;
    }

    /**
     * 获取OSA分销数据产品明细
     * @param responseId
     * @param cotcName
     * @return
     */
    public List<UnileverOsaDetail> getOsaDistributionProductDetail(String responseId, String cotcName){

        LambdaQueryWrapper<UnileverOsaDetail> unileverOsaDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverOsaDetailLambdaQueryWrapper.eq(UnileverOsaDetail::getResponseId, responseId);
        unileverOsaDetailLambdaQueryWrapper.eq(UnileverOsaDetail::getCotcName, cotcName);
        List<UnileverOsaDetail> unileverOsaDetailList = unileverOsaDetailMapper.selectList(unileverOsaDetailLambdaQueryWrapper);

        return unileverOsaDetailList;
    }

    /**
     * 获取Tdp分销数据  图像识别结果表数据量大，故代码中归类运算以减轻数据库压力
     * @param responseId
     * @return
     */
    public List<UnileverTdp> getTdpDistribution(String responseId){

        List<UnileverTdp> unileverTdpList = new ArrayList<>();
        //聚合后数据
        List<UnileverTdp> unileverTdpListDTO = new ArrayList<>();
        LambdaQueryWrapper<UnileverTdp> unileverTdpLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverTdpLambdaQueryWrapper.eq(UnileverTdp::getResponseId, responseId);
        unileverTdpList = unileverTdpMapper.selectList(unileverTdpLambdaQueryWrapper);
        if (unileverTdpList.size() > 0){
            Map<String, UnileverTdp> unileverTdpMap = initialTdpData();
            int totalCountWashingSecret = 0;
            int totalCountWashingGold = 0;
            int totalCountWashingPlanet = 0;
            int totalCountCleaningSecret = 0;
            int totalCountCleaningPlanet = 0;
            for (UnileverTdp unileverTdp: unileverTdpList){
                if (CATEGORY_FABRIC_WASHING.equals(unileverTdp.getCategory()) && BRAND_SECRET.equals(unileverTdp.getBrand())){
                    totalCountWashingSecret = totalCountWashingSecret + unileverTdp.getCount();
                }else if (CATEGORY_FABRIC_WASHING.equals(unileverTdp.getCategory()) && BRAND_GOLD_SPINNING.equals(unileverTdp.getBrand())){
                    totalCountWashingGold = totalCountWashingGold + unileverTdp.getCount();
                }else if (CATEGORY_FABRIC_WASHING.equals(unileverTdp.getCategory()) && BRAND_HUAMU_PLANET.equals(unileverTdp.getBrand())){
                    totalCountWashingPlanet = totalCountWashingPlanet + unileverTdp.getCount();
                }else if (CATEGORY_HOME_CLEANING.equals(unileverTdp.getCategory()) && BRAND_SECRET.equals(unileverTdp.getBrand())){
                    totalCountCleaningSecret = totalCountCleaningSecret + unileverTdp.getCount();
                }else if (CATEGORY_HOME_CLEANING.equals(unileverTdp.getCategory()) && BRAND_HUAMU_PLANET.equals(unileverTdp.getBrand())){
                    totalCountCleaningPlanet = totalCountCleaningPlanet + unileverTdp.getCount();
                }
            }
            UnileverTdp unileverTdpWashingSecret = unileverTdpMap.get(CATEGORY_FABRIC_WASHING + BRAND_SECRET);
            unileverTdpWashingSecret.setCount(totalCountWashingSecret);
            unileverTdpListDTO.add(unileverTdpWashingSecret);

            UnileverTdp unileverTdpWashingGold = unileverTdpMap.get(CATEGORY_FABRIC_WASHING + BRAND_GOLD_SPINNING);
            unileverTdpWashingGold.setCount(totalCountWashingGold);
            unileverTdpListDTO.add(unileverTdpWashingGold);

            UnileverTdp unileverTdpWashingPlanet = unileverTdpMap.get(CATEGORY_FABRIC_WASHING + BRAND_HUAMU_PLANET);
            unileverTdpWashingPlanet.setCount(totalCountWashingPlanet);
            unileverTdpListDTO.add(unileverTdpWashingPlanet);

            UnileverTdp unileverTdpCleaningSecret = unileverTdpMap.get(CATEGORY_HOME_CLEANING + BRAND_SECRET);
            unileverTdpCleaningSecret.setCount(totalCountCleaningSecret);
            unileverTdpListDTO.add(unileverTdpCleaningSecret);

            UnileverTdp unileverTdpCleaningPlanet = unileverTdpMap.get(CATEGORY_HOME_CLEANING + BRAND_HUAMU_PLANET);
            unileverTdpCleaningPlanet.setCount(totalCountCleaningPlanet);
            unileverTdpListDTO.add(unileverTdpCleaningPlanet);
        }

        return unileverTdpListDTO;
    }

    /**
     * 获取Tdp分销数据产品明细
     * @param responseId
     * @param brandType
     * @return
     */
    public List<UnileverTdp> getTdpDistributionProductDetail(String responseId, String category, String brandType){

        LambdaQueryWrapper<UnileverTdp> unileverTdpLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverTdpLambdaQueryWrapper.eq(UnileverTdp::getResponseId, responseId);
        unileverTdpLambdaQueryWrapper.eq(UnileverTdp::getCategory, category);
        unileverTdpLambdaQueryWrapper.eq(UnileverTdp::getBrand, brandType);
        List<UnileverTdp> unileverTdpList = unileverTdpMapper.selectList(unileverTdpLambdaQueryWrapper);

        return unileverTdpList;
    }

    /**
     * 获取CTA POSM数据
     * @param responseId
     * @return
     */
    public List<CtaPosm> getCtaPosm(String responseId){

        List<CtaPosm> ctaPosmList = new ArrayList<>();
        List<CtaPosm> ctaPosmListNew = new ArrayList<>();
        LambdaQueryWrapper<UnileverCtaPosm> unileverCtaPosmLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverCtaPosmLambdaQueryWrapper.eq(UnileverCtaPosm::getResponseId, responseId);
        List<UnileverCtaPosm> unileverCtaPosmList = unileverCtaPosmMapper.selectList(unileverCtaPosmLambdaQueryWrapper);
        if (unileverCtaPosmList != null && unileverCtaPosmList.size() > 0){
            ctaPosmList = CglibCopyBeanUtil.doBatchClone(unileverCtaPosmList, CtaPosm.class);
            Map<String, CtaPosm> ctaPosmMap = initialCtaPosm();
            for (CtaPosm ctaPosm: ctaPosmList){
                CtaPosm ctaPosmTarget = ctaPosmMap.get(ctaPosm.getBrand());
                if (ctaPosmTarget != null){
                    ctaPosmTarget.setDistributionType((ctaPosm.getCount()!=null && ctaPosm.getCount()>0)?DISTRIBUTION_YES:DISTRIBUTION_NO);
                    ctaPosmTarget.setCount(ctaPosm.getCount());
                    //如果本竞品关系变化 以数据为准
                    ctaPosmTarget.setBrandType(ctaPosm.getBrandType());
                }
            }
            ctaPosmList = ctaPosmMap.values().stream().collect(Collectors.toList());
            ctaPosmList = ctaPosmList.stream().sorted(Comparator.comparing(CtaPosm::getDistributionType).reversed()).collect(Collectors.toList());
        }
        return ctaPosmList;
    }

    /**
     * 获取SOS货架品牌占比
     * @param responseId
     * @return
     */
    public List<UnileverSos> getSosBrandRatio(String responseId){

        LambdaQueryWrapper<UnileverSos> unileverSosLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverSosLambdaQueryWrapper.eq(UnileverSos::getResponseId, responseId);
        List<UnileverSos> unileverSosList = unileverSosMapper.selectList(unileverSosLambdaQueryWrapper);

        return unileverSosList;
    }

    /**
     * 获取价签信息
     * @param responseId
     * @return
     */
    public List<UnileverPricetag> getPriceTag(String responseId){

        LambdaQueryWrapper<UnileverPricetag> unileverPricetagLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverPricetagLambdaQueryWrapper.eq(UnileverPricetag::getResponseId, responseId);
        //分销排在上部
        unileverPricetagLambdaQueryWrapper.orderByDesc(UnileverPricetag::getIfDist);
        List<UnileverPricetag> unileverPricetagList = unileverPricetagMapper.selectList(unileverPricetagLambdaQueryWrapper);

        return unileverPricetagList;
    }

    /**
     * 获取二级陈列
     * @param responseId
     * @return
     */
    public SecondaryDisplay getSecondaryDisplay(String responseId){

        SecondaryDisplay secondaryDisplay = new SecondaryDisplay();
        LambdaQueryWrapper<UnileverDisplayPosm> unileverDisplayPosmLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unileverDisplayPosmLambdaQueryWrapper.eq(UnileverDisplayPosm::getResponseId, responseId);

        List<UnileverDisplayPosm> unileverDisplayPosmList = unileverDisplayPosmMapper.selectList(unileverDisplayPosmLambdaQueryWrapper);
        if (unileverDisplayPosmList != null && unileverDisplayPosmList.size() > 0){
            List<SelfProductPosm> selfProductPosmList = new ArrayList<>();
            List<UnileverDisplayPosm> pileOfGroundList = new ArrayList<>();
            List<UnileverDisplayPosm> otherSecondaryDisplaysList = new ArrayList<>();
            boolean isDistributionSecret = false;
            boolean isDistributionGold = false;
            boolean isDistributionPlanet = false;
            for (int i = 0; i < unileverDisplayPosmList.size(); i ++){
                UnileverDisplayPosm unileverDisplayPosm = unileverDisplayPosmList.get(i);
                if (SCENE_PILE_OF_GROUND.equals(unileverDisplayPosm.getScene())){
                    pileOfGroundList.add(unileverDisplayPosm);
                }else {
                    otherSecondaryDisplaysList.add(unileverDisplayPosm);
                }
                if (unileverDisplayPosm.getBrand().contains(BRAND_SECRET) && !isDistributionSecret){
                    isDistributionSecret = true;
                }
                if (unileverDisplayPosm.getBrand().contains(BRAND_GOLD_SPINNING) && !isDistributionGold){
                    isDistributionGold = true;
                }
                if (unileverDisplayPosm.getBrand().contains(BRAND_HUAMU_PLANET) && !isDistributionPlanet){
                    isDistributionPlanet = true;
                }
            }
            Map<String, SelfProductPosm> selfProductPosmMap = initialSelfProductPosm();
            //奥妙
            SelfProductPosm selfProductPosmSecret = selfProductPosmMap.get(BRAND_SECRET);
            if (isDistributionSecret){
                selfProductPosmSecret.setWithOrWithout(DISTRIBUTION_YES);
            }
            selfProductPosmList.add(selfProductPosmSecret);
            //金纺
            SelfProductPosm selfProductPosmGold = selfProductPosmMap.get(BRAND_GOLD_SPINNING);
            if (isDistributionGold){
                selfProductPosmGold.setWithOrWithout(DISTRIBUTION_YES);
            }
            selfProductPosmList.add(selfProductPosmGold);
            //花木星球
            SelfProductPosm selfProductPosmPlanet = selfProductPosmMap.get(BRAND_HUAMU_PLANET);
            if (isDistributionPlanet){
                selfProductPosmPlanet.setWithOrWithout(DISTRIBUTION_YES);
            }
            selfProductPosmList.add(selfProductPosmPlanet);
            secondaryDisplay.setSelfProductPosmList(selfProductPosmList);
            secondaryDisplay.setPileOfGroundList(pileOfGroundList);
            secondaryDisplay.setOtherSecondaryDisplaysList(otherSecondaryDisplaysList);
        }
        return secondaryDisplay;
    }

    /**
     * 根据rid获取门店编号
     * @param responseId
     * @return
     */
    public String getAddressIDnum(String responseId){

        LambdaQueryWrapper<TResponse> responseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        responseLambdaQueryWrapper.eq(TResponse::getId, responseId);
        TResponse tResponse = responseMapper.selectOne(responseLambdaQueryWrapper);

        LambdaQueryWrapper<TTasklaunch> tasklaunchLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tasklaunchLambdaQueryWrapper.eq(TTasklaunch::getTaskid, tResponse.getTaskid());
        TTasklaunch tasklaunch = tasklaunchMapper.selectOne(tasklaunchLambdaQueryWrapper);
        //门店编码
        String addressIdNum = tasklaunch.getAddressIDnum();

        return addressIdNum;
    }

    /**
     * 初始化TDP基础数据
     * @return
     */
    public Map<String, UnileverTdp> initialTdpData(){

        Map<String, UnileverTdp> unileverTdpMap = new HashMap<>();

        UnileverTdp unileverTdp = new UnileverTdp();
        unileverTdp.setCategory(CATEGORY_FABRIC_WASHING);
        unileverTdp.setBrand(BRAND_SECRET);
        unileverTdpMap.put(CATEGORY_FABRIC_WASHING + BRAND_SECRET, unileverTdp);

        UnileverTdp unileverTdp1 = new UnileverTdp();
        unileverTdp1.setCategory(CATEGORY_FABRIC_WASHING);
        unileverTdp1.setBrand(BRAND_GOLD_SPINNING);
        unileverTdpMap.put(CATEGORY_FABRIC_WASHING + BRAND_GOLD_SPINNING, unileverTdp1);

        UnileverTdp unileverTdp2 = new UnileverTdp();
        unileverTdp2.setCategory(CATEGORY_FABRIC_WASHING);
        unileverTdp2.setBrand(BRAND_HUAMU_PLANET);
        unileverTdpMap.put(CATEGORY_FABRIC_WASHING + BRAND_HUAMU_PLANET, unileverTdp2);

        UnileverTdp unileverTdp3 = new UnileverTdp();
        unileverTdp3.setCategory(CATEGORY_HOME_CLEANING);
        unileverTdp3.setBrand(BRAND_SECRET);
        unileverTdpMap.put(CATEGORY_HOME_CLEANING + BRAND_SECRET, unileverTdp3);

        UnileverTdp unileverTdp4 = new UnileverTdp();
        unileverTdp4.setCategory(CATEGORY_HOME_CLEANING);
        unileverTdp4.setBrand(BRAND_HUAMU_PLANET);
        unileverTdpMap.put(CATEGORY_HOME_CLEANING + BRAND_HUAMU_PLANET, unileverTdp4);

        return unileverTdpMap;
    }

    /**
     * 初始化本品POSM数据
     * @return
     */
    public Map<String, SelfProductPosm> initialSelfProductPosm(){

        Map<String, SelfProductPosm> selfProductPosmMap = new HashMap<>();

        //奥妙
        SelfProductPosm selfProductPosmSecret = new SelfProductPosm();
        selfProductPosmSecret.setBrandName(BRAND_SECRET);
        selfProductPosmSecret.setWithOrWithout(DISTRIBUTION_NO);
        selfProductPosmMap.put(BRAND_SECRET, selfProductPosmSecret);

        //金纺
        SelfProductPosm selfProductPosmGold = new SelfProductPosm();
        selfProductPosmGold.setBrandName(BRAND_GOLD_SPINNING);
        selfProductPosmGold.setWithOrWithout(DISTRIBUTION_NO);
        selfProductPosmMap.put(BRAND_GOLD_SPINNING, selfProductPosmGold);

        //花木星球
        SelfProductPosm selfProductPosmPlanet = new SelfProductPosm();
        selfProductPosmPlanet.setBrandName(BRAND_HUAMU_PLANET);
        selfProductPosmPlanet.setWithOrWithout(DISTRIBUTION_NO);
        selfProductPosmMap.put(BRAND_HUAMU_PLANET, selfProductPosmPlanet);

        return selfProductPosmMap;
    }

    /**
     * 初始化本品CTA数据
     * @return
     */
    public Map<String, CtaPosm> initialCtaPosm(){

        Map<String, CtaPosm> ctaPosmMap = new HashMap<>();

        //奥妙
        CtaPosm ctaPosmSecret = new CtaPosm();
        ctaPosmSecret.setBrand(BRAND_SECRET);
        ctaPosmSecret.setBrandType(1);
        ctaPosmMap.put(BRAND_SECRET, ctaPosmSecret);

        //金纺
        CtaPosm ctaPosmGold = new CtaPosm();
        ctaPosmGold.setBrand(BRAND_GOLD_SPINNING);
        ctaPosmGold.setBrandType(1);
        ctaPosmMap.put(BRAND_GOLD_SPINNING, ctaPosmGold);

        //花木星球
        CtaPosm ctaPosmPlanet = new CtaPosm();
        ctaPosmPlanet.setBrand(BRAND_HUAMU_PLANET);
        ctaPosmPlanet.setBrandType(1);
        ctaPosmMap.put(BRAND_HUAMU_PLANET, ctaPosmPlanet);

        //立白
        CtaPosm ctaPosmLiby = new CtaPosm();
        ctaPosmLiby.setBrand(BRAND_COMPETITOR_LIBY);
        ctaPosmLiby.setBrandType(0);
        ctaPosmMap.put(BRAND_COMPETITOR_LIBY, ctaPosmLiby);

        //雕牌
        CtaPosm ctaPosmEngraving = new CtaPosm();
        ctaPosmEngraving.setBrand(BRAND_COMPETITOR_ENGRAVING);
        ctaPosmEngraving.setBrandType(0);
        ctaPosmMap.put(BRAND_COMPETITOR_ENGRAVING, ctaPosmEngraving);

        return ctaPosmMap;
    }

    /**
     * 获取二次陈列图片
     * @param responseId
     * @param repeatNo
     * @return
     */
    public List<SecondDisplayImageDTO> getSecondDisplayImage(String responseId, String repeatNo){

        List<SecondDisplayImageDTO> secondDisplayImageDTOList = unileverSecondDisplayImageMapper.getSecondDisplayImages(responseId, repeatNo);

        return secondDisplayImageDTOList;
    }

    /**
     * 获取识别结果
     *
     * @param responseId
     * @return
     */
    public UnileverResp getUnileverResult(String responseId) {
        UnileverResp resp = new UnileverResp();
        UnileverRolloutStoreRecord record = unileverRolloutStoreRecordMapper.getByResponseId(responseId);
        if (Objects.isNull(record)) {
            return resp;
        }

        resp.setVisitId(record.getVisitId());
        resp.setStartTime(DateUtil.convert2String(record.getStartTime(), "yy-MM-dd HH:mm:ss"));
        resp.setEndTime(DateUtil.convert2String(record.getEndTime(), "yy-MM-dd HH:mm:ss"));
        resp.setStoreCode(record.getStoreCode());
        resp.setStoreName(record.getStoreName());
        resp.setChannelCode(record.getChannelCode());

        String images = record.getUrl();
        if (StringUtils.isNotBlank(images)) {
            List<String> imageList = Arrays.asList(images.split(";"));
            resp.setImageList(imageList);
        }

        List<UnileverRolloutOsa> osaList = unileverRolloutOsaMapper.getByResponseId(responseId);
        List<UnileverRolloutTdp> tdpList = unileverRolloutTdpMapper.getByResponseId(responseId);
        List<UnileverRolloutTdpDetail> tdpDetailList = unileverRolloutTdpDetailMapper.getByResponseId(responseId);

        List<UnileverOsa> unileverOsaList = Lists.newArrayList();
        List<com.lenztech.bi.enterprise.dto.unilever.UnileverTdp> unileverTdpList = Lists.newArrayList();
        List<UnileverTdpDetail> unileverTdpDetailList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(osaList)) {
            for (UnileverRolloutOsa osa : osaList) {
                UnileverOsa unileverOsa = new UnileverOsa();
                unileverOsa.setCotcCategoryName(osa.getCotcCategoryName());
                unileverOsa.setCotcCategoryDetail(osa.getCotcCategoryDetail());
                unileverOsa.setBrand(osa.getBrand());
                unileverOsa.setCotcCode(osa.getCotcCode());
                unileverOsa.setCotcSkuDescription(osa.getCotcSkuDescription());
                unileverOsa.setCategoryCode(osa.getCategoryCode());
                unileverOsa.setCategoryName(osa.getCategoryName());
                unileverOsa.setCategoryNameEn(osa.getCategoryNameEn());
                unileverOsa.setAvailability(osa.getAvailability());
                unileverOsaList.add(unileverOsa);
            }
        }

        if (CollectionUtils.isNotEmpty(tdpList)) {
            for (UnileverRolloutTdp tdp : tdpList) {
                com.lenztech.bi.enterprise.dto.unilever.UnileverTdp unileverTdp = new com.lenztech.bi.enterprise.dto.unilever.UnileverTdp();
                unileverTdp.setCategoryCode(tdp.getCategoryCode());
                unileverTdp.setCategoryNameCn(tdp.getCategoryNameCn());
                unileverTdp.setCategoryNameEn(tdp.getCategoryNameEn());
                unileverTdp.setSubCategoryCode(tdp.getSubCategoryCode());
                unileverTdp.setSubCategoryName(tdp.getSubCategoryName());
                unileverTdp.setSubCategoryNameEn(tdp.getSubCategoryNameEn());
                unileverTdp.setBarcodeCount(tdp.getBarcodeCount());
                unileverTdpList.add(unileverTdp);
            }
        }

        if (CollectionUtils.isNotEmpty(tdpDetailList)) {
            for (UnileverRolloutTdpDetail tdpDetail : tdpDetailList) {
                UnileverTdpDetail unileverTdpDetail = new UnileverTdpDetail();
                unileverTdpDetail.setCategoryCode(tdpDetail.getCategoryCode());
                unileverTdpDetail.setCategoryNameCn(tdpDetail.getCategoryNameCn());
                unileverTdpDetail.setCategoryNameEn(tdpDetail.getCategoryNameEn());
                unileverTdpDetail.setSubCategoryCode(tdpDetail.getSubCategoryCode());
                unileverTdpDetail.setSubCategoryName(tdpDetail.getSubCategoryName());
                unileverTdpDetail.setSubCategoryNameEn(tdpDetail.getSubCategoryNameEn());
                unileverTdpDetail.setUlCode(tdpDetail.getUlCode());
                unileverTdpDetail.setBarCode(tdpDetail.getBarCode());
                unileverTdpDetail.setSkuName(tdpDetail.getSkuName());
                unileverTdpDetail.setIfPs(tdpDetail.getIfPs());
                unileverTdpDetailList.add(unileverTdpDetail);
            }
        }
        resp.setUnileverOsaList(unileverOsaList);
        resp.setUnileverTdpList(unileverTdpList);
        resp.setUnileverTdpDetailList(unileverTdpDetailList);
        return resp;
    }
}
