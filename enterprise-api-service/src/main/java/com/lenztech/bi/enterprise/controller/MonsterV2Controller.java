package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.monsterV2.MonsterBiTargetResp;
import com.lenztech.bi.enterprise.dto.monsterV2.ReportFormsReq;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.service.MonsterAppService;
import com.lenztech.bi.enterprise.service.MonsterV2ResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 魔爪获取BI识别结果
 *
 * <AUTHOR>
 * @date 2022-02-16 11:42
 */
@RestController
@RequestMapping("/biResult/monsterV2/")
@Slf4j
public class MonsterV2Controller {

    @Autowired
    private MonsterV2ResultService monsterV2ResultService;

    @RequestMapping(value = "getTargetList", method = RequestMethod.POST)
    @ControllerAnnotation(use = "【魔爪二期】查询bi结果")
    public ResponseData<MonsterBiTargetResp> getBiTargetList(@RequestBody ReportFormsReq req) {
        try {
            return ResponseData.success(monsterV2ResultService.getTargetList(req));
        } catch (Exception e) {
            log.info("【魔爪二期】查询bi结果异常!", e);
        }
        return ResponseData.failure();
    }

    /**
     * 魔爪回调DCS客户端识别结果集
     *
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getBiTargetList", method = RequestMethod.GET)
    public ResponseData<ApiResultDTO> getBiTargetList(String responseId) {
        log.info("【魔爪回调DCS客户端查询结果请求】, responseId:{}", responseId);
        try {
            return ResponseData.success().data(monsterV2ResultService.getBiTargetList(responseId));
        } catch (Exception e) {
            log.error("【魔爪回调DCS客户端查询结果请求失败!】", e);
        }
        return ResponseData.failure();
    }

}
