package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.ghp.GhpPocListDTO;
import com.lenztech.bi.enterprise.service.GhpEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 金红叶H5演示Demo查询BI数据接口
 * @Date 2020/2/4 20:24
 **/

@RestController
@RequestMapping("/biResult/ghp")
public class GhpResultController {

    @Autowired
    private GhpEnterpriseService ghpEnterpriseService;

    @GetMapping("/listGhpPocEntity/{responseId}/{imageId}")
    public ResponseData<GhpPocListDTO> listPgPocSkuNotExistEntity(@PathVariable("responseId") String responseId, @PathVariable("imageId") String imageId) {
        return ghpEnterpriseService.listGhyPocEntity(responseId, imageId);
    }
}
