package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.ferrero.FerreroBiTargetResp;
import com.lenztech.bi.enterprise.service.FerreroReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 费列罗bi指标Controller
 *
 * <AUTHOR>
 * @date 2021-01-19 12:44:19
 */
@RestController
@RequestMapping("/biResult/ferrero/")
public class FerreroBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(FerreroBiResultController.class);

    @Autowired
    private FerreroReportService ferreroReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<FerreroBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<FerreroBiTargetResp> get(String responseId) {
        try {
            FerreroBiTargetResp ferreroBiTargetResp = ferreroReportService.getBiTargetList(responseId);
            return ResponseData.success().data(ferreroBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
