package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.BIResultRet;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.SnowBeerCompleteStatusResp;
import com.lenztech.bi.enterprise.dto.hipac.HiPacBiTargetResp;
import com.lenztech.bi.enterprise.dto.hipac.HipacCompleteStatusResp;
import com.lenztech.bi.enterprise.service.HiPacReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description: 海拍客获取BI结果
 * @Author: jialipan
 * @date 2021-05-24 09:44:19
 */
@RestController
@RequestMapping("/biResult/hipac/")
public class HipacBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(HipacBiResultController.class);

    @Autowired
    private HiPacReportService hiPacReportService;

    /**
     * 根据答卷id查询BI结果
     *
     * @param responseId 答卷Id
     * @return ResponseData
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取海拍客识别结果")
    public ResponseData<BIResultRet> biResult(@RequestParam("responseId") String responseId) {
        try {
            HiPacBiTargetResp hiPacBiTargetResp = hiPacReportService.getBiTargetList(responseId);
            return ResponseData.success().data(hiPacBiTargetResp);
        } catch (Exception e) {
            logger.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 根据答卷id查询BI状态
     *
     * @param responseId 答卷Id
     * @return ResponseData
     */
    @RequestMapping(value = "getCompleteStatus", method = RequestMethod.GET)
    public ResponseData<HipacCompleteStatusResp> getCompleteStatus(String responseId) {
        try {
            HipacCompleteStatusResp hipacCompleteStatusResp = hiPacReportService.getCompleteStatus(responseId);
            return ResponseData.success().data(hipacCompleteStatusResp);
        } catch (Exception e) {
            logger.error("/getResult========", e);
        }
        return ResponseData.failure();
    }
}
