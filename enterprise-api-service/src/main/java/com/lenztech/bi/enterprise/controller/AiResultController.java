package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.base.BaseController;
import com.lenztech.bi.enterprise.dto.RequestData;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.http.request.AIResultRequest;
import com.lenztech.bi.enterprise.http.request.AppealSubmitRequest;
import com.lenztech.bi.enterprise.http.response.AppealResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

//@RestController
//@Api(value = "企业厂商 AI 识别结果查询接口")
//@RequestMapping("business/api/lenztech/enterprise/")
public class AiResultController extends BaseController {
 
    @ApiOperation(value = "企业厂商 AI 识别结果查询接口")
    @PostMapping("getImgAIResult")
    public ResponseData<AppealResultResponse> getImgAIResult(@RequestBody RequestData<AIResultRequest> requestData) {

        return ResponseData.success();
    }
}