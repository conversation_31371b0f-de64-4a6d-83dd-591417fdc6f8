package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/11 15:26
 **/
@RestController
@RequestMapping("/operateLog")
public class OperateLogController {
//    @Autowired
//    private OperateLogService operateLogService;

    /***
     * @description: 此业务不用了，注释掉访问task库内容，等lenz调用bi的逻辑删除后可删除此接口
     */
    @GetMapping("/saveRecord")
    public ResponseData saveRecord(@RequestParam("userId") String userId, @RequestParam("platform") String platform, @RequestParam("operateEvent") String operateEvent) {
//        operateLogService.saveRecord(userId, platform, operateEvent);
        return ResponseData.success();
    }


}
