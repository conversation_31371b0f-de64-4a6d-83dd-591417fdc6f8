package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.liby.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/9 14:51
 * @since JDK 1.8
 */
public interface LiByEnterpriseService {

    /**
     * 核心门店明细
     * @param coreStoreDetailReqDTO
     * @return
     */
    ResponseData<CoreStoreDetailRespDTO> listCoreStoreDetail(CoreStoreDetailReqDTO coreStoreDetailReqDTO);

    /**
     * 核心门店扣分某一项店铺
     * @param coreStoreDetailReqDTO
     * @return
     */
    ResponseData<PointsAreaDTO> listPointsArea(CoreStoreDetailReqDTO coreStoreDetailReqDTO);

    /**
     * 门店得分查询
     * @param coreStoreDetailReqDTO
     * @return
     */
    ResponseData<StoreScoreDetailRespDTO> getStoreScoreDetail(CoreStoreDetailReqDTO coreStoreDetailReqDTO);

    /**
     * 获取区域-城市-门店信息
     * @return
     */
    ResponseData<StoreDTO> getStoreList();

}
