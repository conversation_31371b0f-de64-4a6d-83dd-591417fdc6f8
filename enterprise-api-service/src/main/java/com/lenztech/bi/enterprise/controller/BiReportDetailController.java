//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
//import com.lenztech.bi.enterprise.dto.RequestData;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.bi.BiReportDetailReq;
//import com.lenztech.bi.enterprise.dto.bi.GetSkuDetailReq;
//import com.lenztech.bi.enterprise.service.BiComputeDataService;
//import com.lenztech.bi.enterprise.service.BiSettingService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.io.IOException;
//
///**
// * @Description:BI报表重构设置
// * @Author: zhangjie
// * @Date: 3/18/20 AM10:30
// */
//@RestController
//@RequestMapping("/biReport")
//public class BiReportDetailController {
//
//    public static final Logger LOGGER = LoggerFactory.getLogger(BiReportDetailController.class);
//
//    @Autowired
//    private BiSettingService biSettingService;
//
//    @Autowired
//    private BiComputeDataService computeDetailDataService;
//
//    /**
//     * 报表-bi报表详情接口
//     *
//     * @param detailReqRequest
//     * @return
//     */
//    @RequestMapping(value = "/detail", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "报表-bi报表详情接口")
//    public ResponseData reportDetail(@RequestBody RequestData<BiReportDetailReq> detailReqRequest) throws IOException {
//        if (detailReqRequest == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.reportDetail(detailReqRequest.getReqobj());
//    }
//
//    /**
//     * 计算bi详情中间表计算
//     *
//     * @param responseId 答卷id
//     * @return
//     */
//    @RequestMapping(value = "/computeQingPiDetail", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "报表-计算bi详情中间表计算")
//    public ResponseData computeQingPiDetail(String responseId) throws IOException {
//        if (responseId == null){
//            return ResponseData.failure();
//        }
//        computeDetailDataService.computeQingPiDetail(responseId);
//
//        return ResponseData.success();
//    }
//
//    @RequestMapping(value = "/answerTime", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "答题时间计算")
//    public ResponseData answerTime(String responseId) throws IOException {
//        if (responseId == null){
//            return ResponseData.failure();
//        }
//        return ResponseData.success().data(computeDetailDataService.answerTime(responseId));
//    }
//
//    /**
//     * 计算bi详情中间表计算
//     *
//     * @param taskId 答卷id
//     * @return
//     */
//    @RequestMapping(value = "/computeQingPiHistory", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "报表-计算bi详情中间表计算")
//    public ResponseData computeQingPiHistory(String taskId) throws IOException {
//        if (taskId == null){
//            return ResponseData.failure();
//        }
//        computeDetailDataService.computeQingPiHistory(taskId);
//
//        return ResponseData.success();
//    }
//
//
//    /**
//     * 识别服务获取 SKU 详情接口
//     *
//     * @param getSkuDetailReqRequest
//     * @return
//     */
//    @RequestMapping(value = "/getSkuDetail", method = RequestMethod.POST)
//    @ControllerAnnotation(use = "报表-bi报表详情接口")
//    public ResponseData getSkuDetail(@RequestBody RequestData<GetSkuDetailReq> getSkuDetailReqRequest) throws IOException {
//        if (getSkuDetailReqRequest == null){
//            return ResponseData.failure();
//        }
//        return biSettingService.getSkuDetail(getSkuDetailReqRequest.getReqobj());
//    }
//
//}
