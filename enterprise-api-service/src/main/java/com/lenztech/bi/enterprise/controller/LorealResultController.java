//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.loreal.OuLaiYaSkuListDTO;
//import com.lenztech.bi.enterprise.service.LorealEnterpriseService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * 欧莱雅查询BI数据接口
// * <AUTHOR>
// * @version V1.0
// * @date 2019-10-23 16:25
// * @since JDK 1.8
// */
//@RestController
//@RequestMapping("/biResult/loreal")
//public class LorealResultController {
//
//    private LorealEnterpriseService lorealEnterpriseService;
//
//    @Autowired
//    public LorealResultController(LorealEnterpriseService lorealEnterpriseService) {
//        this.lorealEnterpriseService = lorealEnterpriseService;
//    }
//
//    @GetMapping("/listOuLaiYaSku/{responseId}")
//    public ResponseData<OuLaiYaSkuListDTO> listOuLaiYaSku(@PathVariable("responseId") String responseId) {
//        return lorealEnterpriseService.listOuLaiYaSku(responseId);
//    }
//
//}
