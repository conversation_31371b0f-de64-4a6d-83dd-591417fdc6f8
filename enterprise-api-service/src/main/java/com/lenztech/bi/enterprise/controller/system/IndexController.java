//package com.lenztech.bi.enterprise.controller.system;
//
//import org.apache.shiro.SecurityUtils;
//import org.apache.shiro.subject.Subject;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//
///**
// * @Title: LoginController
// * @Description:
// * @author: ailikes
// * @version: 1.0
// * @date: 2018/11/20 11:39
// */
//@Controller
//public class IndexController {
//
//    private Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @RequestMapping("login")
//    public String tologin(){
//        logger.info("定向登陆页");
//        return "login";
//    }
//
//    @RequestMapping
//    public String home(){
//        logger.info("定向主页");
//        return "home";
//    }
//
//    @RequestMapping("logout")
//    public String logout(){
//        logger.info("退出系统");
//        Subject subject = SecurityUtils.getSubject();
//        subject.logout(); // shiro底层删除session的会话信息
//        return "redirect:login";
//    }
//
//}
