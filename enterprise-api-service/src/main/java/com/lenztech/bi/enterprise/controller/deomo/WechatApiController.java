package com.lenztech.bi.enterprise.controller.deomo;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.demo.GetShelfSituationReq;
import com.lenztech.bi.enterprise.dto.demo.HandleQuestionReq;
import com.lenztech.bi.enterprise.service.demo.WechatApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/biResult/wechatApi/")
public class WechatApiController {
    
    @Autowired
    private WechatApiService wechatApiService;
    /**
     * 获取品类列表
     * @return
     */
    @RequestMapping(value = "getCategoryList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取品类列表")
    public ResponseData getCategoryList() {
        try {
            return ResponseData.success().data(wechatApiService.getCategoryList());
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 获取货架情况列表
     * @return
     */
    @RequestMapping(value = "getShelfSituationList", method = RequestMethod.POST)
    @ControllerAnnotation(use = "获取货架情况列表")
    public ResponseData getShelfSituationList(@RequestBody GetShelfSituationReq getShelfSituationReq) {
        try {
            return ResponseData.success().data(wechatApiService.getShelfSituationList(getShelfSituationReq));
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 获取货架详情
     * @param submitId
     * @return
     */
    @RequestMapping(value = "getCategoryDetailList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取货架详情")
    public ResponseData getCategoryDetailList(String submitId) {
        try {
            return ResponseData.success().data(wechatApiService.getCategoryDetailList(submitId));
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 已完成或上报
     * @param handleQuestionReq
     * @return
     */
    @RequestMapping(value = "handleQuestion", method = RequestMethod.POST)
    @ControllerAnnotation(use = "已完成或上报")
    public ResponseData handleQuestion(@RequestBody HandleQuestionReq handleQuestionReq) {
        try {
            wechatApiService.handleQuestion(handleQuestionReq);
            return ResponseData.success();
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 获取上报原因列表
     * @return
     */
    @RequestMapping(value = "getReasonList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取上报原因列表")
    public ResponseData getReasonList() {
        try {
            return ResponseData.success().data(wechatApiService.getReasonList());
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 待处理任务品类列表
     * @return
     */
    @RequestMapping(value = "getWaitHandleList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "待处理任务品类列表")
    public ResponseData getWaitHandleList() {
        try {
            return ResponseData.success().data(wechatApiService.getWaitHandleList());
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 品类内待处理任务列表
     * @return
     */
    @RequestMapping(value = "getHandleRecordList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "品类内待处理任务列表")
    public ResponseData getHandleRecordList() {
        try {
            return ResponseData.success().data(wechatApiService.getHandleRecordList());
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 商品详情
     * @param productId
     * @return
     */
    @RequestMapping(value = "getRecordDetail", method = RequestMethod.GET)
    @ControllerAnnotation(use = "商品详情")
    public ResponseData getRecordDetail(String productId, String submitId) {
        try {
            return ResponseData.success().data(wechatApiService.getRecordDetail(productId, submitId));
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 对比表格
     * @param productId
     * @return
     */
    @RequestMapping(value = "getCompareDetail", method = RequestMethod.GET)
    @ControllerAnnotation(use = "对比表格")
    public ResponseData getCompareDetail(String productId) {
        try {
            return ResponseData.success().data(wechatApiService.getCompareDetail(productId));
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
    /**
     * 排面走势
     * @param productId
     * @return
     */
    @RequestMapping(value = "geFaceTrend", method = RequestMethod.GET)
    @ControllerAnnotation(use = "排面走势")
    public ResponseData geFaceTrend(String productId) {
        try {
            return ResponseData.success().data(wechatApiService.geFaceTrend(productId));
        } catch (Exception e) {
            log.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
}
