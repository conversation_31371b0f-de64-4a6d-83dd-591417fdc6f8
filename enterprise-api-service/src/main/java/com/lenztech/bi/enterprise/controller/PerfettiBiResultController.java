package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.SnowBeerBiResultResp;
import com.lenztech.bi.enterprise.dto.SnowBeerCompleteStatusResp;
import com.lenztech.bi.enterprise.dto.perfetti.PerfettiResult;
import com.lenztech.bi.enterprise.service.PerfettiService;
import com.lenztech.bi.enterprise.service.SnowBeerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 旺旺bi指标Controller
 *
 * <AUTHOR>
 * @date 2019-10-17 15:44:19
 */
@RestController
@RequestMapping("/biResult/perfetti/")
@Slf4j
public class PerfettiBiResultController {

    @Autowired
    private PerfettiService perfettiService;

    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<SnowBeerBiResultResp> getResult(String responseId) {
        try {
            PerfettiResult perfettiResult = perfettiService.getResult(responseId);
            return ResponseData.success().data(perfettiResult);
        } catch (Exception e) {
            log.error("/getResult========", e);
        }
        return ResponseData.failure();
    }

}
