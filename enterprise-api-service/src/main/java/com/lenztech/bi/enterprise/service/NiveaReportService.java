package com.lenztech.bi.enterprise.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lenztech.bi.enterprise.dto.nivea.GetGlobalDataReq;
import com.lenztech.bi.enterprise.dto.nivea.GetGlobalDataResp;
import com.lenztech.bi.enterprise.dto.nivea.GetIdentifyListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetProductListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetQueueSizeResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreBranchListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreDistrictListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreRetailerListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreStateListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreTypeListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreRegionListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetUserListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetTargetListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetVisitTypeListResp;
import com.lenztech.bi.enterprise.dto.nivea.QueueAckReq;
import com.lenztech.bi.enterprise.dto.nivea.QueueAckResp;
import com.lenztech.bi.enterprise.dto.nivea.RouteResp;
import com.lenztech.bi.enterprise.dto.nivea.RouteResp.Metadata;
import com.lenztech.bi.enterprise.dto.nivea.RouteResp.Links;
import com.lenztech.bi.enterprise.dto.nivea.RouteResp.RouteItem;
import com.lenztech.bi.enterprise.dto.nivea.RouteResp.Entities;
import com.lenztech.bi.enterprise.entity.NiveaGlobalProduct;
import com.lenztech.bi.enterprise.entity.NiveaGlobalStore;
import com.lenztech.bi.enterprise.entity.NiveaGlobalUser;
import com.lenztech.bi.enterprise.entity.NiveaGlobalRoute;
import com.lenztech.bi.enterprise.entity.NiveaIdentifyGlobal;
import com.lenztech.bi.enterprise.mapper.NiveaGlobalProductMapper;
import com.lenztech.bi.enterprise.mapper.NiveaGlobalStoreMapper;
import com.lenztech.bi.enterprise.mapper.NiveaGlobalUserMapper;
import com.lenztech.bi.enterprise.mapper.NiveaGlobalRouteMapper;
import com.lenztech.bi.enterprise.mapper.NiveaIdentifyGlobalMapper;
import com.lenztech.bi.enterprise.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 妮维雅Service
 *
 * <AUTHOR>
 * @date 2024-04-12 15:44:19
 */
@Slf4j
@Service
public class NiveaReportService {

    private final NiveaGlobalStoreMapper niveaGlobalStoreMapper;

    private final NiveaGlobalProductMapper niveaGlobalProductMapper;

    private final NiveaGlobalUserMapper niveaGlobalUserMapper;

    @Autowired
    private NiveaGlobalRouteMapper niveaGlobalRouteMapper;

    @Autowired
    private NiveaIdentifyGlobalMapper niveaIdentifyGlobalMapper;

    public NiveaReportService(NiveaGlobalStoreMapper niveaGlobalStoreMapper, NiveaGlobalProductMapper niveaGlobalProductMapper, NiveaGlobalUserMapper niveaGlobalUserMapper) {
        this.niveaGlobalStoreMapper = niveaGlobalStoreMapper;
        this.niveaGlobalProductMapper = niveaGlobalProductMapper;
        this.niveaGlobalUserMapper = niveaGlobalUserMapper;
    }

    public GetStoreListResp getStoreList(Integer pageNo, Integer pageSize) {
        // 1. 获取所有门店数据
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        Integer count = niveaGlobalStoreMapper.selectCount(wrapper);
        wrapper.last(" order by store_number limit " + ((pageNo - 1) * pageSize) + ", " + pageSize);
        List<NiveaGlobalStore> list = niveaGlobalStoreMapper.selectList(wrapper);
        
        // 2. 构建响应对象
        GetStoreListResp resp = new GetStoreListResp();
        GetStoreListResp.Metadata metadata = new GetStoreListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(count / pageSize + 1);
        metadata.setTotal_count(count);
        
        // 3. 设置分页链接
        GetStoreListResp.Metadata.Links links = new GetStoreListResp.Metadata.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store?sort=store_number&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < (count / pageSize + 1) ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(baseUrl + (count / pageSize + 1) + "&per_page=" + pageSize);
        metadata.setLinks(links);
        resp.setMetadata(metadata);
        
        // 4. 构建门店列表
        List<GetStoreListResp.StoreDTO> storeDTOList = new ArrayList<>();
        for (NiveaGlobalStore niveaGlobalStore : list) {
            GetStoreListResp.StoreDTO storeDTO = new GetStoreListResp.StoreDTO();
            storeDTO.setStore_number(niveaGlobalStore.getStoreNumber());
            storeDTO.setStore_name(niveaGlobalStore.getStoreName());
            storeDTO.setStore_display_name(niveaGlobalStore.getStoreName());
            storeDTO.setStore_type_name(niveaGlobalStore.getStoreType());
            storeDTO.setRegion_name(niveaGlobalStore.getRegion());
            storeDTO.setDistrict_name(null);
            storeDTO.setBranch_name(null);
            storeDTO.setRetailer_name(niveaGlobalStore.getRetailer());
            storeDTO.setState_code(null);
            storeDTO.setAudit_cycle_set(null);
            storeDTO.setStreet(niveaGlobalStore.getStreet());
            storeDTO.setAddress_line_2(null);
            storeDTO.setCity(niveaGlobalStore.getCity());
            storeDTO.setPostal_code(null);
            storeDTO.setLatitude(null);
            storeDTO.setLongitude(null);
            storeDTO.setIs_active("1".equals(niveaGlobalStore.getActive()));
            storeDTO.setManager_name(null);
            storeDTO.setManager_email(null);
            storeDTO.setManager_phone(niveaGlobalStore.getPhone());
            storeDTO.setLast_update_time(null);
            storeDTO.setIs_deleted(false);
            
            // 设置额外属性
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("additional_attribute_1", niveaGlobalStore.getAttribute1());
            jsonObject.put("additional_attribute_2", niveaGlobalStore.getAttribute2());
            jsonObject.put("additional_attribute_3", niveaGlobalStore.getAttribute3());
            jsonObject.put("additional_attribute_4", niveaGlobalStore.getAttribute4());
            jsonObject.put("additional_attribute_5", niveaGlobalStore.getAttribute5());
            jsonObject.put("additional_attribute_6", niveaGlobalStore.getAttribute6());
            jsonObject.put("additional_attribute_7", niveaGlobalStore.getAttribute7());
            jsonObject.put("additional_attribute_8", niveaGlobalStore.getAttribute8());
            jsonObject.put("additional_attribute_9", niveaGlobalStore.getAttribute9());
            jsonObject.put("additional_attribute_10", niveaGlobalStore.getAttribute10());
            jsonObject.put("additional_attribute_11", null);
            jsonObject.put("additional_attribute_21", null);
            jsonObject.put("additional_attribute_22", null);
            jsonObject.put("additional_attribute_23", null);
            jsonObject.put("additional_attribute_24", null);
            jsonObject.put("additional_attribute_25", null);
            jsonObject.put("additional_attribute_26", null);
            jsonObject.put("additional_attribute_27", null);
            jsonObject.put("additional_attribute_28", null);
            jsonObject.put("additional_attribute_29", null);
            storeDTO.setStore_additional_attributes(jsonObject);
            
            storeDTOList.add(storeDTO);
        }
        resp.setStore(storeDTOList);
        
        return resp;
    }

    public GetStoreBranchListResp getStoreBranchList(Integer pageNo, Integer pageSize) {
        // 1. 先获取所有不重复的branch
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(NiveaGlobalStore::getBranch)
                .isNotNull(NiveaGlobalStore::getBranch)
                .groupBy(NiveaGlobalStore::getBranch);
        List<NiveaGlobalStore> allBranches = niveaGlobalStoreMapper.selectList(wrapper);
        
        // 2. 计算总数和分页信息
        int totalCount = allBranches.size();
        int pageCount = (totalCount + pageSize - 1) / pageSize;
        
        // 3. 手动分页，确保索引不会越界（pageNo从1开始）
        int startIndex = Math.min((pageNo - 1) * pageSize, totalCount);
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<NiveaGlobalStore> pagedBranches = startIndex < endIndex ? 
            allBranches.subList(startIndex, endIndex) : new ArrayList<>();
        
        // 4. 构建响应对象
        GetStoreBranchListResp resp = new GetStoreBranchListResp();
        GetStoreBranchListResp.Metadata metadata = new GetStoreBranchListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(pageCount);
        metadata.setTotal_count(totalCount);
        
        // 5. 设置分页链接
        GetStoreBranchListResp.Links links = new GetStoreBranchListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store/branch?sort=branch_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < pageCount ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(pageCount > 0 ? baseUrl + pageCount + "&per_page=" + pageSize : "");
        metadata.setLinks(links);
        resp.setMetadata(metadata);
        
        // 6. 构建分支列表
        List<GetStoreBranchListResp.Branch> branchList = new ArrayList<>();
        for (NiveaGlobalStore store : pagedBranches) {
            GetStoreBranchListResp.Branch branch = new GetStoreBranchListResp.Branch();
            branch.setBranch_code(null);
            branch.setBranch_name(store.getBranch());
            branch.setIs_deleted(false);
            branchList.add(branch);
        }
        resp.setBranch(branchList);
        
        return resp;
    }

    public GetStoreDistrictListResp getStoreDistrictList(Integer pageNo, Integer pageSize) {
        // 1. 先获取所有不重复的district
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(NiveaGlobalStore::getDistrict)
                .isNotNull(NiveaGlobalStore::getDistrict)
                .groupBy(NiveaGlobalStore::getDistrict);
        List<NiveaGlobalStore> allDistricts = niveaGlobalStoreMapper.selectList(wrapper);

        // 2. 计算总数和分页信息
        int totalCount = allDistricts.size();
        int pageCount = (totalCount + pageSize - 1) / pageSize;

        // 3. 手动分页，确保索引不会越界（pageNo从1开始）
        int startIndex = Math.min((pageNo - 1) * pageSize, totalCount);
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<NiveaGlobalStore> pagedDistricts = startIndex < endIndex ?
                allDistricts.subList(startIndex, endIndex) : new ArrayList<>();

        // 4. 构建响应对象
        GetStoreDistrictListResp resp = new GetStoreDistrictListResp();
        GetStoreDistrictListResp.Metadata metadata = new GetStoreDistrictListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(pageCount);
        metadata.setTotal_count(totalCount);

        // 5. 设置分页链接
        GetStoreDistrictListResp.Links links = new GetStoreDistrictListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store/district?sort=district_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < pageCount ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(pageCount > 0 ? baseUrl + pageCount + "&per_page=" + pageSize : "");
        metadata.setLinks(links);
        resp.setMetadata(metadata);

        // 6. 构建区域列表
        List<GetStoreDistrictListResp.District> districtList = new ArrayList<>();
        for (NiveaGlobalStore store : pagedDistricts) {
            GetStoreDistrictListResp.District district = new GetStoreDistrictListResp.District();
            district.setDistrict_code(null);
            district.setDistrict_name(store.getDistrict());
            district.setIs_deleted(false);
            districtList.add(district);
        }
        resp.setDistrict(districtList);

        return resp;
    }

    public GetStoreRetailerListResp getStoreRetailerList(Integer pageNo, Integer pageSize) {
        // 1. 先获取所有不重复的retailer
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(NiveaGlobalStore::getRetailer)
                .isNotNull(NiveaGlobalStore::getRetailer)
                .groupBy(NiveaGlobalStore::getRetailer);
        List<NiveaGlobalStore> allRetailers = niveaGlobalStoreMapper.selectList(wrapper);

        // 2. 计算总数和分页信息
        int totalCount = allRetailers.size();
        int pageCount = (totalCount + pageSize - 1) / pageSize;

        // 3. 手动分页，确保索引不会越界（pageNo从1开始）
        int startIndex = Math.min((pageNo - 1) * pageSize, totalCount);
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<NiveaGlobalStore> pagedRetailers = startIndex < endIndex ?
                allRetailers.subList(startIndex, endIndex) : new ArrayList<>();

        // 4. 构建响应对象
        GetStoreRetailerListResp resp = new GetStoreRetailerListResp();
        GetStoreRetailerListResp.Metadata metadata = new GetStoreRetailerListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(pageCount);
        metadata.setTotal_count(totalCount);

        // 5. 设置分页链接
        GetStoreRetailerListResp.Links links = new GetStoreRetailerListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store/retailer?sort=retailer_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < pageCount ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(pageCount > 0 ? baseUrl + pageCount + "&per_page=" + pageSize : "");
        metadata.setLinks(links);
        resp.setMetadata(metadata);

        // 6. 构建零售商列表
        List<GetStoreRetailerListResp.Retailer> retailerList = new ArrayList<>();
        for (NiveaGlobalStore store : pagedRetailers) {
            GetStoreRetailerListResp.Retailer retailer = new GetStoreRetailerListResp.Retailer();
            retailer.setRetailer_code(null);
            retailer.setRetailer_name(store.getRetailer());
            retailer.setIs_deleted(false);
            retailerList.add(retailer);
        }
        resp.setRetailer(retailerList);

        return resp;
    }

    public GetStoreStateListResp getStoreStateList(Integer pageNo, Integer pageSize) {
        // 1. 先获取所有不重复的state
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(NiveaGlobalStore::getState)
                .isNotNull(NiveaGlobalStore::getState)
                .groupBy(NiveaGlobalStore::getState);
        List<NiveaGlobalStore> allStates = niveaGlobalStoreMapper.selectList(wrapper);

        // 2. 计算总数和分页信息
        int totalCount = allStates.size();
        int pageCount = (totalCount + pageSize - 1) / pageSize;

        // 3. 手动分页，确保索引不会越界（pageNo从1开始）
        int startIndex = Math.min((pageNo - 1) * pageSize, totalCount);
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<NiveaGlobalStore> pagedStates = startIndex < endIndex ?
                allStates.subList(startIndex, endIndex) : new ArrayList<>();

        // 4. 构建响应对象
        GetStoreStateListResp resp = new GetStoreStateListResp();
        GetStoreStateListResp.Metadata metadata = new GetStoreStateListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(pageCount);
        metadata.setTotal_count(totalCount);

        // 5. 设置分页链接
        GetStoreStateListResp.Links links = new GetStoreStateListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store/state?sort=state_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < pageCount ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(pageCount > 0 ? baseUrl + pageCount + "&per_page=" + pageSize : "");
        metadata.setLinks(links);
        resp.setMetadata(metadata);

        // 6. 构建州/省列表
        List<GetStoreStateListResp.State> stateList = new ArrayList<>();
        for (NiveaGlobalStore store : pagedStates) {
            GetStoreStateListResp.State state = new GetStoreStateListResp.State();
            state.setState_code(null);
            state.setState_name(store.getState());
            state.setIs_deleted(false);
            stateList.add(state);
        }
        resp.setState(stateList);

        return resp;
    }

    public GetStoreTypeListResp getStoreTypeList(Integer pageNo, Integer pageSize) {
        // 1. 先获取所有不重复的store_type
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(NiveaGlobalStore::getStoreType)
                .isNotNull(NiveaGlobalStore::getStoreType)
                .groupBy(NiveaGlobalStore::getStoreType);
        List<NiveaGlobalStore> allStoreTypes = niveaGlobalStoreMapper.selectList(wrapper);

        // 2. 计算总数和分页信息
        int totalCount = allStoreTypes.size();
        int pageCount = (totalCount + pageSize - 1) / pageSize;

        // 3. 手动分页，确保索引不会越界（pageNo从1开始）
        int startIndex = Math.min((pageNo - 1) * pageSize, totalCount);
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<NiveaGlobalStore> pagedStoreTypes = startIndex < endIndex ?
                allStoreTypes.subList(startIndex, endIndex) : new ArrayList<>();

        // 4. 构建响应对象
        GetStoreTypeListResp resp = new GetStoreTypeListResp();
        GetStoreTypeListResp.Metadata metadata = new GetStoreTypeListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(pageCount);
        metadata.setTotal_count(totalCount);

        // 5. 设置分页链接
        GetStoreTypeListResp.Links links = new GetStoreTypeListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store/store_type?sort=store_type_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < pageCount ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(baseUrl + pageCount + "&per_page=" + pageSize);
        metadata.setLinks(links);
        resp.setMetadata(metadata);

        // 6. 构建门店类型列表
        List<GetStoreTypeListResp.StoreType> storeTypeList = new ArrayList<>();
        for (NiveaGlobalStore store : pagedStoreTypes) {
            GetStoreTypeListResp.StoreType storeType = new GetStoreTypeListResp.StoreType();
            storeType.setStore_type_code(null);
            storeType.setStore_type_name(store.getStoreType());
            storeType.setIs_deleted(false);
            storeTypeList.add(storeType);
        }
        resp.setStore_type(storeTypeList);

        return resp;
    }

    public GetStoreRegionListResp getStoreRegionList(Integer pageNo, Integer pageSize) {
        // 1. 先获取所有不重复的region
        LambdaQueryWrapper<NiveaGlobalStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(NiveaGlobalStore::getRegion, NiveaGlobalStore::getCountry)
                .isNotNull(NiveaGlobalStore::getRegion)
                .groupBy(NiveaGlobalStore::getRegion);
        List<NiveaGlobalStore> allRegions = niveaGlobalStoreMapper.selectList(wrapper);

        // 2. 计算总数和分页信息
        int totalCount = allRegions.size();
        int pageCount = (totalCount + pageSize - 1) / pageSize;

        // 3. 手动分页，确保索引不会越界（pageNo从1开始）
        int startIndex = Math.min((pageNo - 1) * pageSize, totalCount);
        int endIndex = Math.min(startIndex + pageSize, totalCount);
        List<NiveaGlobalStore> pagedRegions = startIndex < endIndex ?
                allRegions.subList(startIndex, endIndex) : new ArrayList<>();

        // 4. 构建响应对象
        GetStoreRegionListResp resp = new GetStoreRegionListResp();
        GetStoreRegionListResp.Metadata metadata = new GetStoreRegionListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(pageCount);
        metadata.setTotal_count(totalCount);

        // 5. 设置分页链接
        GetStoreRegionListResp.Links links = new GetStoreRegionListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/store/region?sort=region_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < pageCount ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(baseUrl + pageCount + "&per_page=" + pageSize);
        metadata.setLinks(links);
        resp.setMetadata(metadata);

        // 6. 构建区域列表
        List<GetStoreRegionListResp.Region> regionList = new ArrayList<>();
        for (NiveaGlobalStore store : pagedRegions) {
            GetStoreRegionListResp.Region region = new GetStoreRegionListResp.Region();
            region.setRegion_code(null);
            region.setRegion_name(store.getRegion());
            region.setCountry_name(store.getCountry());
            region.setIs_deleted(false);
            regionList.add(region);
        }
        resp.setRegion(regionList);

        return resp;
    }

    public GetProductListResp getProductList(Integer pageNo, Integer pageSize) {
        // 1. 获取所有产品数据
        LambdaQueryWrapper<NiveaGlobalProduct> wrapper = new LambdaQueryWrapper<>();
        Integer count = niveaGlobalProductMapper.selectCount(wrapper);
        wrapper.last(" order by id limit " + ((pageNo - 1) * pageSize) + ", " + pageSize);
        List<NiveaGlobalProduct> list = niveaGlobalProductMapper.selectList(wrapper);
        
        // 2. 构建响应对象
        GetProductListResp resp = new GetProductListResp();
        GetProductListResp.Metadata metadata = new GetProductListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(count / pageSize + 1);
        metadata.setTotal_count(count);
        
        // 3. 设置分页链接
        GetProductListResp.Metadata.Links links = new GetProductListResp.Metadata.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/product?sort=id&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < (count / pageSize + 1) ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(baseUrl + (count / pageSize + 1) + "&per_page=" + pageSize);
        metadata.setLinks(links);
        resp.setMetadata(metadata);
        
        // 4. 构建产品列表
        List<GetProductListResp.ProductDTO> productDTOList = new ArrayList<>();
        for (NiveaGlobalProduct product : list) {
            GetProductListResp.ProductDTO productDTO = new GetProductListResp.ProductDTO();
            productDTO.setPk(product.getId());
            productDTO.setProduct_item_code(product.getBarcode());
            productDTO.setProduct_client_code(product.getEanCode());
            productDTO.setProduct_uuid(product.getProductId());
            productDTO.setProduct_name(product.getEnglishSkuName());
            productDTO.setProduct_local_name(product.getLocalSkuName());
            productDTO.setProduct_short_name(product.getEnglishSkuName());
//            productDTO.setProduct_global_status_name(null);
//            productDTO.setBrand_pk(null);
            productDTO.setBrand_name(product.getBrandEnglishName());
            productDTO.setBrand_local_name(null);
//            productDTO.setManufacturer_pk(null);
            productDTO.setManufacturer_name("Beiersdorf");
            productDTO.setManufacturer_local_name(null);
            productDTO.setIs_deleted(false);
            productDTO.setIs_active(true);
            productDTO.setLast_update_time(DateUtil.convert2String(product.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
//            productDTO.setCategory_pk(null);
            productDTO.setCategory_name(product.getCategoryEnglishName());
            productDTO.setCategory_local_name(null);
            productDTO.setSubcategory_name(product.getSubCategory());
            productDTO.setSubcategory_local_name(product.getSubCategoryLocalName());
            productDTO.setContainer_type(null);
            productDTO.setSize(Integer.valueOf(product.getSize()));
            productDTO.setUnit_measurement(product.getSizeUnit());
            productDTO.setNumber_of_subpackages(null);
            productDTO.setUnits(null);
            productDTO.setDiscovered_by_brand_watch(null);
            Map<String, String> map = new HashMap<>();
            map.put("attribute1", product.getAttribute1());
            map.put("attribute3", product.getAttribute3());
            map.put("attribute5", product.getAttribute5());
            map.put("attribute6", product.getAttribute6());
            map.put("attribute7", product.getAttribute7());
            productDTO.setProduct_additional_attributes(map);
            productDTO.setAlt_code(null);
            productDTO.setProduct_type(null);
            productDTO.setWidth(null);
            productDTO.setHeight(null);

//            // 设置图片信息
//            GetProductListResp.ProductDTO.Image image = new GetProductListResp.ProductDTO.Image();
//            image.setImage_url(null);
//            image.setDirection(null);
            productDTO.setImages(Lists.newArrayList());

//            // 设置替代设计信息
//            GetProductListResp.ProductDTO.AlternativeDesigns alternativeDesigns = new GetProductListResp.ProductDTO.AlternativeDesigns();
//            alternativeDesigns.setAlternative_design_name(null);
//            alternativeDesigns.setStart_date(null);
//            alternativeDesigns.setEnd_date(null);
//            alternativeDesigns.setImages(Lists.newArrayList());

//            List<GetProductListResp.ProductDTO.AlternativeDesigns> alternativeDesignsList = new ArrayList<>();
//            alternativeDesignsList.add(alternativeDesigns);
            productDTO.setAlternative_designs(Lists.newArrayList());

            productDTOList.add(productDTO);
        }
        resp.setProduct(productDTOList);
        
        return resp;
    }

    public GetUserListResp getUserList(Integer pageNo, Integer pageSize) {
        // 1. 获取所有用户数据
        LambdaQueryWrapper<NiveaGlobalUser> wrapper = new LambdaQueryWrapper<>();
        Integer count = niveaGlobalUserMapper.selectCount(wrapper);
        wrapper.last(" order by id limit " + ((pageNo - 1) * pageSize) + ", " + pageSize);
        List<NiveaGlobalUser> list = niveaGlobalUserMapper.selectList(wrapper);
        
        // 2. 构建响应对象
        GetUserListResp resp = new GetUserListResp();
        GetUserListResp.Metadata metadata = new GetUserListResp.Metadata();
        metadata.setPage(pageNo);
        metadata.setPer_page(pageSize);
        metadata.setPage_count(count / pageSize + 1);
        metadata.setTotal_count(count);
        
        // 3. 设置分页链接
        GetUserListResp.Links links = new GetUserListResp.Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/user?sort=login_name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + pageSize);
        links.setFirst(baseUrl + "1&per_page=" + pageSize);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + pageSize : "");
        links.setNext(pageNo < (count / pageSize + 1) ? baseUrl + (pageNo + 1) + "&per_page=" + pageSize : "");
        links.setLast(baseUrl + (count / pageSize + 1) + "&per_page=" + pageSize);
        metadata.setLinks(links);
        resp.setMetadata(metadata);
        
        // 4. 构建用户列表
        List<GetUserListResp.User> userList = new ArrayList<>();
        for (NiveaGlobalUser user : list) {
            GetUserListResp.User userDTO = new GetUserListResp.User();
            userDTO.setLogin_name(user.getPhone());
            userDTO.setEmail_address(user.getPhone());
            userDTO.setFirst_name(user.getUserFirstName());
            userDTO.setLast_name(user.getUserLastName());
            userDTO.setIs_deleted(false);
            userDTO.setOperational_role(user.getMobileApplicationRole());
            userDTO.setSupervisor_username(null);
            
            // 设置entities
            Map<String, List<String>> entities = new HashMap<>();
            if (user.getRouteId() != null) {
                entities.put("routes", Collections.singletonList(user.getRouteId()));
            }
            userDTO.setEntities(entities);
            
            userList.add(userDTO);
        }
        resp.setUser(userList);
        
        return resp;
    }

    public GetTargetListResp getTargetList() {
        GetTargetListResp resp = new GetTargetListResp();
        List<GetTargetListResp.Target> targetList = new ArrayList<>();
        
        GetTargetListResp.Target target = new GetTargetListResp.Target();
        target.setVisit_type_uid("c0bd901c-cec5-4021-939b-ecbddbd585ef");
        target.setDisplay_name("visit_type_default_name");
        targetList.add(target);
        
        resp.setTargets(targetList);
        return resp;
    }

    public GetVisitTypeListResp getVisitTypeList() {
        GetVisitTypeListResp resp = new GetVisitTypeListResp();
        List<GetVisitTypeListResp.VisitType> visitTypeList = new ArrayList<>();
        
        GetVisitTypeListResp.VisitType visitType = new GetVisitTypeListResp.VisitType();
        visitType.setVisit_type_uid("c0bd901c-cec5-4021-939b-ecbddbd585ef");
        visitType.setDisplay_name("visit_type_default_name");
        visitTypeList.add(visitType);
        
        resp.setVisit_types(visitTypeList);
        return resp;
    }

    public RouteResp getRouteList(int pageNo, int perPage) {
        // 1. 获取当前页的路线列表
        LambdaQueryWrapper<NiveaGlobalRoute> wrapper = new LambdaQueryWrapper<>();
        Integer count = niveaGlobalRouteMapper.selectCount(wrapper);
        wrapper.last(" order by id limit " + ((pageNo - 1) * perPage) + ", " + perPage);
        List<NiveaGlobalRoute> list = niveaGlobalRouteMapper.selectList(wrapper);
        
        // 2. 收集路线ID列表
        List<String> routeIds = list.stream()
                .map(NiveaGlobalRoute::getRouteId)
                .collect(Collectors.toList());
        
        // 3. 一次性查询所有相关门店
        LambdaQueryWrapper<NiveaGlobalStore> storeWrapper = new LambdaQueryWrapper<>();
        storeWrapper.in(NiveaGlobalStore::getRouteId, routeIds)
                .select(NiveaGlobalStore::getRouteId, NiveaGlobalStore::getStoreNumber);
        List<NiveaGlobalStore> allStores = niveaGlobalStoreMapper.selectList(storeWrapper);
        
        // 4. 一次性查询所有相关用户
        LambdaQueryWrapper<NiveaGlobalUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(NiveaGlobalUser::getRouteId, routeIds)
                .select(NiveaGlobalUser::getRouteId, NiveaGlobalUser::getPhone);
        List<NiveaGlobalUser> allUsers = niveaGlobalUserMapper.selectList(userWrapper);
        
        // 5. 按路线ID分组
        Map<String, List<String>> routeStoresMap = allStores.stream()
                .collect(Collectors.groupingBy(
                    NiveaGlobalStore::getRouteId,
                    Collectors.mapping(NiveaGlobalStore::getStoreNumber, Collectors.toList())
                ));
        
        Map<String, List<String>> routeUsersMap = allUsers.stream()
                .collect(Collectors.groupingBy(
                    NiveaGlobalUser::getRouteId,
                    Collectors.mapping(NiveaGlobalUser::getPhone, Collectors.toList())
                ));
        
        // 6. 组装返回数据
        List<RouteItem> routes = new ArrayList<>();
        for (NiveaGlobalRoute dbRoute : list) {
            RouteItem item = new RouteItem();
            item.setExternal_route_id(dbRoute.getRouteId());
            item.setName(dbRoute.getRouteName());
            item.setIs_deleted(false);
            
            Entities entities = new Entities();
            entities.setCycles(new ArrayList<>());
            entities.setStores(routeStoresMap.getOrDefault(dbRoute.getRouteId(), new ArrayList<>()));
            entities.setUsers(routeUsersMap.getOrDefault(dbRoute.getRouteId(), new ArrayList<>()));
            
            item.setEntities(entities);
            routes.add(item);
        }
        
        // 7. 组装分页信息
        RouteResp resp = new RouteResp();
        Metadata metadata = new Metadata();
        Links links = new Links();
        String baseUrl = "/api/v1/beiersdorfch/entity/routes?sort=name&page=";
        links.setSelf(baseUrl + pageNo + "&per_page=" + perPage);
        links.setFirst(baseUrl + "1&per_page=" + perPage);
        links.setPrevious(pageNo > 1 ? baseUrl + (pageNo - 1) + "&per_page=" + perPage : "");
        links.setNext(pageNo < (count / perPage + 1) ? baseUrl + (pageNo + 1) + "&per_page=" + perPage : "");
        links.setLast(baseUrl + (count / perPage + 1) + "&per_page=" + perPage);
        metadata.setLinks(links);
        metadata.setPage(pageNo);
        metadata.setPage_count(count / perPage + 1);
        metadata.setPer_page(perPage);
        metadata.setTotal_count(count);
        resp.setMetadata(metadata);
        resp.setRoutes(routes);
        return resp;
    }

    /**
     * 妮维雅项目-global拉取数据接口
     *
     * @param from 开始时间
     * @param to 结束时间
     * @param page 页码
     * @param perPage 每页数量
     * @return 识别结果列表
     */
    public GetIdentifyListResp getIdentifyList(String from, String to, Integer page, Integer perPage) {

        // 2. 处理时间参数
        if (StringUtils.isNotBlank(from)) {
            from = DateUtil.convert2String(new Date(Long.parseLong(from) * 1000), "yyyy-MM-dd HH:mm:ss");
        }
        if (StringUtils.isNotBlank(to)) {
            to = DateUtil.convert2String(new Date(Long.parseLong(to) * 1000), "yyyy-MM-dd HH:mm:ss");
        }
        int start = (page - 1) * perPage;
        int size = perPage;

        try {
            // 4. 构建查询条件
            LambdaQueryWrapper<NiveaIdentifyGlobal> wrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(from)) {
                wrapper.ge(NiveaIdentifyGlobal::getVisitTime, from);
            }
            if (StringUtils.isNotBlank(to)) {
                wrapper.lt(NiveaIdentifyGlobal::getVisitTime, to);
            }
            wrapper.orderByAsc(NiveaIdentifyGlobal::getId);
            wrapper.last("limit " + start + ", " + size);

            // 5. 查询数据
            List<NiveaIdentifyGlobal> resultList = niveaIdentifyGlobalMapper.selectList(wrapper);
            int totalCount = niveaIdentifyGlobalMapper.selectCount(wrapper);

            // 6. 构建返回结果
            GetIdentifyListResp resp = new GetIdentifyListResp();
            resp.setFrom_time(from);
            resp.setTo_time(to);

            // 7. 构建metadata
            GetIdentifyListResp.Metadata metadata = new GetIdentifyListResp.Metadata();
            metadata.setPage(page);
            metadata.setPer_page(perPage);
            metadata.setPage_count(totalCount / size + 1);
            metadata.setTotal_count(totalCount);
            resp.setMetadata(metadata);

            // 8. 构建结果列表
            List<GetIdentifyListResp.IdentifyResult> results = new ArrayList<>();
            for (NiveaIdentifyGlobal item : resultList) {
                GetIdentifyListResp.IdentifyResult result = new GetIdentifyListResp.IdentifyResult();
                result.setSession_uid(item.getSessionUid());
                result.setClient_session_uid("");
                result.setProject_name("beiersdorfch");
                result.setStore_number(item.getStoreNumber());
                result.setExternal_route_id("");
                result.setSession_date(DateUtil.convert2String(item.getVisitTime(), "yyyy-MM-dd"));
                result.setSession_start_time(DateUtil.convert2String(item.getVisitTime(), "yyyy-MM-dd HH:mm:ss"));
                result.setVisitor_identifier("");

                GetIdentifyListResp.ResultsMetadata resultsMetadata = new GetIdentifyListResp.ResultsMetadata();
                resultsMetadata.setGeneration_time(DateUtil.convert2String(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                resultsMetadata.setVersion(1);
                resultsMetadata.setStatus("completed");
                resultsMetadata.setSections_included(Arrays.asList("recognized_items", "kpis"));
                result.setResults_metadata(resultsMetadata);
                result.setResults_link("https://beiersdorfch.langjtech.com/api/v1/beiersdorfch/analysis-results/file/download?id=" + item.getResponseId());

                results.add(result);
            }

            resp.setResults_generated(results.size());
            resp.setResults(results);

            return resp;
        } catch (Exception e) {
            log.error("【妮维雅项目global拉取数据】异常! ", e);
            throw new RuntimeException("获取识别结果列表失败", e);
        }
    }

    /**
     * 获取全局数据
     *
     * @param request 请求参数
     * @return GetGlobalDataResp
     */
    public GetGlobalDataResp queuePull(GetGlobalDataReq request) {
        GetGlobalDataResp resp = new GetGlobalDataResp();
        try {

            // 4. 构建查询条件
            LambdaQueryWrapper<NiveaIdentifyGlobal> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NiveaIdentifyGlobal::getStatus, 1);
            wrapper.last("limit " + request.getMax_count());

            // 5. 查询数据
            List<NiveaIdentifyGlobal> resultList = niveaIdentifyGlobalMapper.selectList(wrapper);

            resp.setResults_requested(request.getMax_count());
            resp.setResults_generated(resultList.size());

            List<GetGlobalDataResp.GlobalDataResult> results = new ArrayList<>();
            List<Long> idList = new ArrayList<>();

            for (NiveaIdentifyGlobal item : resultList) {

                GetGlobalDataResp.GlobalDataResult result = new GetGlobalDataResp.GlobalDataResult();
                idList.add(item.getId());

                result.setSession_uid(item.getSessionUid());
                result.setClient_session_uid("");
                result.setClient_type("On-Device");
                result.setProject_name("beiersdorfch");
                result.setStore_number(item.getStoreNumber());
                result.setExternal_route_id("");
                result.setSession_date(DateUtil.convert2String(item.getVisitTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                result.setSession_start_time(DateUtil.convert2String(item.getVisitTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                result.setVisitor_identifier("");
                GetGlobalDataResp.ResultsMetadata metadata = new GetGlobalDataResp.ResultsMetadata();
                metadata.setMessage_id(item.getId());
                metadata.setGeneration_time(DateUtil.convert2String(item.getCreateTime(), DateUtil.DTFormat.yyyy_MM_dd_HH_mm_ss.getFormat()));
                metadata.setVersion(1);
                metadata.setStatus("completed");
                metadata.setSections_included(Arrays.asList("recognized_items", "kpis"));
                result.setResults_metadata(metadata);
                result.setResults_link("https://trax-ai.oss-cn-beijing.aliyuncs.com/" + item.getResultsLink());
                results.add(result);
            }
            resp.setResults(results);

            // 如果不需要确认，则更新状态
            if (request.getRequire_ack() == null || !request.getRequire_ack()) {
                LambdaQueryWrapper<NiveaIdentifyGlobal> wrapper1 = new LambdaQueryWrapper<>();
                wrapper1.in(NiveaIdentifyGlobal::getId, idList);
                NiveaIdentifyGlobal identifyGlobal = new NiveaIdentifyGlobal();
                identifyGlobal.setStatus(2);
                niveaIdentifyGlobalMapper.update(identifyGlobal, wrapper1);
            }
        } catch (Exception e) {
            log.error("【妮维雅项目global拉取数据】异常! ", e);
        }
        return resp;
    }

    /**
     * 队列ack
     *
     * @param request 请求参数
     * @return QueueAckResp
     */
    public QueueAckResp queueAck(QueueAckReq request) {
        QueueAckResp resp = new QueueAckResp();
        try {
            // 更新状态
            LambdaQueryWrapper<NiveaIdentifyGlobal> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(NiveaIdentifyGlobal::getId, request.getMessage_ids());
            NiveaIdentifyGlobal identifyGlobal = new NiveaIdentifyGlobal();
            identifyGlobal.setStatus(2);
            niveaIdentifyGlobalMapper.update(identifyGlobal, wrapper);

            resp.setSuccessful(request.getMessage_ids());
            resp.setFailed(new ArrayList<>());
        } catch (Exception e) {
            log.error("【beiersdorfch项目】提交ack异常! ", e);
            resp.setSuccessful(new ArrayList<>());
            resp.setFailed(request.getMessage_ids());
        }
        return resp;
    }

    /**
     * 获取队列长度
     *
     * @return GetQueueSizeResp
     */
    public GetQueueSizeResp getQueueSize() {
        GetQueueSizeResp resp = new GetQueueSizeResp();
        try {
            // 查询状态为1的记录数量
            LambdaQueryWrapper<NiveaIdentifyGlobal> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NiveaIdentifyGlobal::getStatus, 1);
            Integer count = niveaIdentifyGlobalMapper.selectCount(wrapper);
            resp.setQueue_size(count);
        } catch (Exception e) {
            log.error("【beiersdorfch项目】获取队列长度异常! ", e);
        }
        return resp;
    }

    /**
     * 获取文件URL
     *
     * @param responseId 响应ID
     * @return 文件URL
     */
    public String getFileUrl(String responseId) {
        try {
            // 查询数据库获取文件链接
            LambdaQueryWrapper<NiveaIdentifyGlobal> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NiveaIdentifyGlobal::getResponseId, responseId);
            NiveaIdentifyGlobal identifyGlobal = niveaIdentifyGlobalMapper.selectOne(wrapper);
            
            if (identifyGlobal == null || StringUtils.isBlank(identifyGlobal.getResultsLink())) {
                return null;
            }
            
            return "https://trax-ai.oss-cn-beijing.aliyuncs.com/" + identifyGlobal.getResultsLink();
        } catch (Exception e) {
            log.error("【beiersdorfch项目】获取文件URL异常! ", e);
            return null;
        }
    }

}
