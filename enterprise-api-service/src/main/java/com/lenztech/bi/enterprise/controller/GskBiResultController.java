package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.gsk.GskBiTargetResp;
import com.lenztech.bi.enterprise.service.GskReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * gsk-bi指标Controller
 *
 * <AUTHOR>
 * @date 2021-05-28 15:44:19
 */
@Slf4j
@RestController
@RequestMapping("/biResult/gsk/")
public class GskBiResultController {

    @Autowired
    private GskReportService gskReportService;

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<GskBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<GskBiTargetResp> get(String responseId) {
        try {
            GskBiTargetResp gskBiTargetResp = gskReportService.getBiTargetList(responseId);
            return ResponseData.success().data(gskBiTargetResp);
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
