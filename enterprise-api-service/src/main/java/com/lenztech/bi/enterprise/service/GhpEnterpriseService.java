package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.ghp.GhpPocListDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-07 15:19
 * @since JDK 1.8
 */
public interface GhpEnterpriseService {

    /**
     * 查询金红叶识别结果
     * @param responseId
     * @return
     */
    ResponseData<GhpPocListDTO> listGhyPocEntity(String responseId, String imageId);

}
