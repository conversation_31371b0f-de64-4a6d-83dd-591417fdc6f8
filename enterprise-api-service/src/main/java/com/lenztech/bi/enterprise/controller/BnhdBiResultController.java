package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneProducts;
import com.lenztech.bi.enterprise.dto.unilever.SecondaryDisplay;
import com.lenztech.bi.enterprise.entity.BnhdPosmsResult;
import com.lenztech.bi.enterprise.entity.BnhdStoreRecord;
import com.lenztech.bi.enterprise.entity.UnileverStoreinfo;
import com.lenztech.bi.enterprise.service.BnhdReportService;
import com.lenztech.bi.enterprise.service.UnileverReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/02/23
 * Time: 17:19
 * 类功能: 百年糊涂poc相关接口
 */
@RestController
@RequestMapping("/biResult/bnhd/")
public class BnhdBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(BnhdBiResultController.class);

    @Autowired
    private BnhdReportService bnhdReportService;

    /**
     * 获取百年糊涂门店相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreinfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<BnhdStoreRecord> getStoreinfo(String responseId) {
        try {
            BnhdStoreRecord bnhdStoreRecord = bnhdReportService.getStoreinfo(responseId);
            return ResponseData.success().data(bnhdStoreRecord);
        } catch (Exception e) {
            logger.error("/getStoreinfo========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取百年糊涂门店POSM结果
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getPosmResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取百年糊涂门店POSM结果")
    public ResponseData getPosmResult(String responseId) {
        try {
            List<BnhdPosmsResult> bnhdPosmsResult = bnhdReportService.getPosmResult(responseId);
            return ResponseData.success().data(bnhdPosmsResult);
        } catch (Exception e) {
            logger.error("/getPosmResult========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取百年糊涂识别场景信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getRecognizeSceneProducts", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取百年糊涂识别场景及其产品信息")
    public ResponseData getRecognizeSceneProducts(String responseId) {
        try {
            List<RecognizeSceneInfo> recognizeSceneInfoList = bnhdReportService.getRecognizeSceneProducts(responseId);
            return ResponseData.success().data(recognizeSceneInfoList);
        } catch (Exception e) {
            logger.error("/getRecognizeSceneProducts========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取百年糊涂识别产品信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getRecognizeProductsInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取百年糊涂识别产品信息")
    public ResponseData getRecognizeProductsInfo(String responseId) {
        try {
            List<ProductPriceInfo> productPriceInfoList = bnhdReportService.getRecognizeProductsInfo(responseId);
            return ResponseData.success().data(productPriceInfoList);
        } catch (Exception e) {
            logger.error("/getRecognizeProductsInfo========", e);
        }
        return ResponseData.failure();
    }


}
