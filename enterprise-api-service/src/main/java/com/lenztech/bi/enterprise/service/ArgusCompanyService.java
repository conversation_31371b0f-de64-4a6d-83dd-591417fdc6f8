//package com.lenztech.bi.enterprise.service;
//
//import com.lenztech.bi.enterprise.dto.CompanyUserDTO;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.bi.CompanyDTO;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
///**
// * @program: bi-service
// * @description: argus关于公司信息方法
// * @author: longgang
// * @create: 2020-09-08 10:20
// **/
//public interface ArgusCompanyService {
//     /**
//      *
//      * @param account  账号  t_user里面的phone与user里面的account对应
//      * @param companyId  公司id
//      * @return
//      */
//     String getRealName(String account,Integer companyId);
//
//    /**
//     * 模糊查询符合要求的公司
//     * @param message
//     * @return
//     */
//    ResponseData getCompanyMessage(String message);
//
//    /**
//     * 模糊查询符合要求的公司
//     * @param message
//     * @return
//     */
//    ResponseData getCompanyId(String message);
//
//    /**
//     * 根据公司名称查询公司信息
//     * @param companyName
//     * @return
//     */
//    CompanyDTO getCompanyMessageByName(String companyName);
//
//    /**
//     * 获取公司用户信息
//     * @param account
//     * @return
//     */
//    List<CompanyUserDTO> getCompanyUser(String account);
//
//}
