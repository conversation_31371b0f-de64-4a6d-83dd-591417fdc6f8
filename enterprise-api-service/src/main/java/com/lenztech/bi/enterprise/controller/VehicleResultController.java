package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.VehicleResultDTO;
import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.entity.JdpResult;
import com.lenztech.bi.enterprise.service.VehicleResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/11/16 16:27
 **/

@Slf4j
@RestController
@RequestMapping("/biResult/vehicle/")
public class VehicleResultController {

    @Autowired
    private VehicleResultService vehicleResultService;


    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<WangWangBiTargetResp>
     */
    @RequestMapping(value = "getVehicleResult", method = RequestMethod.GET)
    public ResponseData<VehicleResultDTO> get(String responseId) {
        try {
            ResponseData<VehicleResultDTO> result = vehicleResultService.getVehicleResult(responseId);
            return result;
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
