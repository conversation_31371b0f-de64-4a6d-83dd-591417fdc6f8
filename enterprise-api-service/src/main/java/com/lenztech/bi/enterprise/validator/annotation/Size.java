package com.lenztech.bi.enterprise.validator.annotation;

import com.lenztech.bi.enterprise.validator.SizeValidatorImpl;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = {SizeValidatorImpl.class}
)
public @interface Size {

    int maxSize() default 32;

    String message() default "长度不合法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
