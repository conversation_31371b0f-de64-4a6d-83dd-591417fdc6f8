package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.liby.*;
import com.lenztech.bi.enterprise.service.LiByEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/9 14:50
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/biResult/liby")
public class LiByResultController {

    private LiByEnterpriseService liByEnterpriseService;

    @Autowired
    public LiByResultController(LiByEnterpriseService liByEnterpriseService) {
        this.liByEnterpriseService = liByEnterpriseService;
    }

    @PostMapping("/listCoreStoreDetail")
    public ResponseData<CoreStoreDetailRespDTO> listCoreStoreDetail(@RequestBody CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        return liByEnterpriseService.listCoreStoreDetail(coreStoreDetailReqDTO);
    }

    @PostMapping("/listPointsArea")
    public ResponseData<PointsAreaDTO> listPointsArea(@RequestBody CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        return liByEnterpriseService.listPointsArea(coreStoreDetailReqDTO);
    }

    @PostMapping("/getStoreScoreDetail")
    public ResponseData<StoreScoreDetailRespDTO> getStoreScoreDetail(@RequestBody CoreStoreDetailReqDTO coreStoreDetailReqDTO) {
        return liByEnterpriseService.getStoreScoreDetail(coreStoreDetailReqDTO);
    }

    @GetMapping("/getStoreList")
    public ResponseData<StoreDTO> getStoreList(){
        return liByEnterpriseService.getStoreList();
    }

}
