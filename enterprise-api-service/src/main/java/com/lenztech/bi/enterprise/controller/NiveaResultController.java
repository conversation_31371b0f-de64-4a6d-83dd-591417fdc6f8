package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.nivea.GetProductListResp;
import com.lenztech.bi.enterprise.dto.nivea.GetStoreListResp;
import com.lenztech.bi.enterprise.service.NiveaReportService;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import com.lenztech.bi.enterprise.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 妮维雅Controller
 *
 * <AUTHOR>
 * @date 2024-04-12 15:44:19
 */
@RestController
@RequestMapping("/biResult/nivea/")
public class NiveaResultController {

    @Autowired
    private NiveaReportService niveaReportService;

    /**
     * 查询门店列表
     *
     * @param page
     * @param per_page
     * @return GetStoreListResp
     */
    @RequestMapping(value = "getStoreList", method = RequestMethod.GET)
    public Object getStoreList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetStoreListResp resp = niveaReportService.getStoreList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }

    /**
     * 查询商品列表
     *
     * @param page
     * @param per_page
     * @return GetProductListResp
     */
    @RequestMapping(value = "getProductList", method = RequestMethod.GET)
    public Object getProductList(HttpServletRequest request, HttpServletResponse response, Integer page, Integer per_page) {
        String apiKey = request.getHeader("Authorization");
        if (StringUtil.isBlank(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "missing Authorization:Auth-Token header value");
            return map;
        }
        if (!"Auth-Token cf2408b302e008a2938d146ea3983619".equals(apiKey)) {
            Map<String, String> map = new HashMap<>();
            map.put("code", "401");
            map.put("description", "the API key is invalid");
            return map;
        }
        GetProductListResp resp = niveaReportService.getProductList(page, per_page);
        String json = JsonUtil.toJsonString(resp);
        response.setHeader("Content-Length", json.getBytes().length + "");
        return resp;
    }
}
