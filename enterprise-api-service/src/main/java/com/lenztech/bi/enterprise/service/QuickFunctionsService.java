package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.utils.HttpConnectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class QuickFunctionsService {

    private static final String TRANSMISSION_AIIDENTIFY_RESULT = "http://tools.ppznet.com/ppz/interface/RepairAiIdentifyIng.php";

    private static final String PENDING_REVIEW_AI_IDENTIFYING = "http://tools.ppznet.com/ppz/interface/PendingReviewAiIdentifyIng.php?task_id={0}";

    private static final String TRANSMISSION_AIIDENTIFY_STATUS = "http://tools.ppznet.com/ppz/interface/RepairAiStatus.php";

    ExecutorService executorService = Executors.newFixedThreadPool(1);

    public ResponseData sendPendingReviewResult(String taskIds) {

        try {
            executorService.execute(() -> {
                HttpConnectionUtils.get(MessageFormat.format(PENDING_REVIEW_AI_IDENTIFYING, taskIds));
            });
        } catch (Exception e) {
            log.error("sendPendingReviewResult========", e);
        }
        return ResponseData.success();
    }

    public ResponseData sendAiIdentifyResult() {

        try {
            executorService.execute(() -> {
                HttpConnectionUtils.get(TRANSMISSION_AIIDENTIFY_RESULT);
            });
        } catch (Exception e) {
            log.error("sendPendingReviewResult========", e);
            return ResponseData.failure();
        }

        try {
            executorService.execute(() -> {
                HttpConnectionUtils.get(TRANSMISSION_AIIDENTIFY_STATUS);
            });
        } catch (Exception e) {
            log.error("sendAiIdentifyResult========", e);
        }
        return ResponseData.success();
    }
}
