package com.lenztech.bi.enterprise.validator;

import com.lenztech.bi.enterprise.validator.annotation.Size;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class SizeValidatorImpl implements ConstraintValidator<Size, Integer> {

    private int maxSize;

    @Override
    public void initialize(Size constraintAnnotation) {
        maxSize = constraintAnnotation.maxSize();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value > 0 && value < maxSize) {
            return true;
        }
        return false;
    }
}