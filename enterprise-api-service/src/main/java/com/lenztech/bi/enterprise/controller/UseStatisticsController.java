//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.dto.GetStatisticsReqDTO;
//import com.lenztech.bi.enterprise.dto.PageDataResult;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.service.IUseStatisticsService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PatchMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.servlet.http.HttpServletResponse;
//
///**
// * <AUTHOR>
// * @Description TODO
// * @Date 2020/9/10 16:47
// **/
//
//@RestController
//@RequestMapping("/useStatistics")
//public class UseStatisticsController {
//
//    @Autowired
//    private IUseStatisticsService iUseStatisticsService;
//
//    @GetMapping("/todayUseStatistics")
//    public ResponseData todayUseStatistics(@RequestParam(value = "date", required = false) String date) {
//        iUseStatisticsService.userUseStatistics(date);
//        return ResponseData.success();
//    }
//
//    @PostMapping("/getOperateStatistics")
//    public ResponseData getOperateStatistics(@RequestBody GetStatisticsReqDTO reqDTO) {
//        return iUseStatisticsService.getOperateStatistics(reqDTO);
//    }
//
//    @GetMapping("/exportOperateStatistics")
//    public ResponseData exportOperateStatistics(String startDate, String endDate, String companyId, HttpServletResponse response) {
//        return iUseStatisticsService.exportOperateStatistics(startDate, endDate, companyId, response);
//    }
//
//    @PostMapping("/getUserUseStatistic")
//    public PageDataResult getUserUseStatistic(@RequestBody GetStatisticsReqDTO reqDTO) {
//        return iUseStatisticsService.getUserUseStatistic(reqDTO);
//    }
//
//    @PostMapping("/getUserResponseList")
//    public ResponseData getUserResponseList(@RequestBody GetStatisticsReqDTO reqDTO) {
//        return iUseStatisticsService.getUserResponseList(reqDTO);
//    }
//
//    @PostMapping("/getStoreList")
//    public ResponseData getStoreList(@RequestBody GetStatisticsReqDTO reqDTO) {
//        return iUseStatisticsService.getStoreList(reqDTO);
//    }
//}
