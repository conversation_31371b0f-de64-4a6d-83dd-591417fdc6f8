package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.base.BaseController;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.http.request.AppealQueryRequest;
import com.lenztech.bi.enterprise.http.request.AppealSkuBsInfo;
import com.lenztech.bi.enterprise.http.request.AppealSkuInfo;
import com.lenztech.bi.enterprise.http.request.AppealSubmitBsRequest;
import com.lenztech.bi.enterprise.http.request.AppealSubmitRequest;
import com.lenztech.bi.enterprise.service.ApiAppealService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description:  申诉模块
 * <AUTHOR>
 * @date  2019-09-10 18:52
 * @since 2019-09-10
 **/
@RestController
@Api(value = "申诉模块")
@RequestMapping({"BISystem","api"})
public class AppealController extends BaseController {

    @Autowired
    private ApiAppealService apiAppealService;
    /**
     * description:  申诉提交接口
     * <AUTHOR>
     * @date  2019-09-10 18:53
     * @since 2019-09-10
     * @param appealSubmitRequest : 
     * @return com.lenztech.bi.enterprise.dto.ResponseData
     **/
    @ApiOperation(value = "提交申诉",response = ResponseData.class)
    @PostMapping("appeal")
    public ResponseData addAppeal(@Valid @RequestBody AppealSubmitRequest appealSubmitRequest) throws Exception {
        Boolean flag = false;
        return apiAppealService.addAppeal(appealSubmitRequest,flag);
    }

    /**
     * description:  百事申诉提交接口
     * <AUTHOR>
     * @date  2019-09-10 18:53
     * @since 2019-09-10
     * @param appealSubmitBsRequest :
     * @return com.lenztech.bi.enterprise.dto.ResponseData
     **/
    @ApiOperation(value = "提交申诉",response = ResponseData.class)
    @PostMapping("appealBs")
    public ResponseData addAppealBs(@Valid @RequestBody AppealSubmitBsRequest appealSubmitBsRequest) throws Exception {
        Boolean flag = true;
        List<AppealSkuInfo> appealSkuInfoList = appealSubmitBsRequest.getSkus().stream().parallel().map(appealSkuBsInfo -> {
            AppealSkuInfo appealSkuInfo = new AppealSkuInfo();
            appealSkuInfo.setExist(appealSkuBsInfo.getExist());
            appealSkuInfo.setFacing(appealSkuBsInfo.getFacing());
            appealSkuInfo.setSkuCode(appealSkuBsInfo.getProductId().toString());
            return appealSkuInfo;
        }).collect(Collectors.toList());

        AppealSubmitRequest appealSubmitRequest = new AppealSubmitRequest();
        appealSubmitRequest.setBusinessDataParamList(appealSubmitBsRequest.getBusinessDataParamList());
        appealSubmitRequest.setCompanyId(appealSubmitBsRequest.getCompanyId());
        appealSubmitRequest.setSkus(appealSkuInfoList);
        appealSubmitRequest.setAppealContent(appealSubmitBsRequest.getAppealContent());

        return apiAppealService.addAppeal(appealSubmitRequest,flag);
    }

    /**
     * description:  申诉结果查询接口
     * <AUTHOR>
     * @date  2019-09-10 19:01
     * @since 2019-09-10
     * @param appealQueryRequest : 
     * @return com.lenztech.bi.enterprise.dto.ResponseData
     **/
    @ApiOperation(value = "申诉查询",response = ResponseData.class)
    @PostMapping("queryAppealResult")
    public ResponseData queryAppealResult(@Valid @RequestBody AppealQueryRequest appealQueryRequest) throws Exception {
        return apiAppealService.queryAppealResult(appealQueryRequest);
    }

}