package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.monsterV2.MonsterBiTargetResp;
import com.lenztech.bi.enterprise.dto.monsterV2.ReportFormsReq;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;

/**
 * 魔爪sku信息表(MonsterPatchesResult)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-16 11:33:41
 */
public interface MonsterV2ResultService {

    /**
     * 魔爪获取BI识别结果
     *
     * @param req
     * @return
     */
    MonsterBiTargetResp getTargetList(ReportFormsReq req);

    /**
     * 根据答卷ID查询bi识别指标结果集
     *
     * @param responseId
     * @return
     */
    ApiResultDTO getBiTargetList(String responseId);

}