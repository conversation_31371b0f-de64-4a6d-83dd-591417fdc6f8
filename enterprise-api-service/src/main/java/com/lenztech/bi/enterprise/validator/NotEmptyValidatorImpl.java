package com.lenztech.bi.enterprise.validator;

import com.lenztech.bi.enterprise.validator.annotation.NotEmpty;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class NotEmptyValidatorImpl implements ConstraintValidator<NotEmpty, String> {


    @Override
    public void initialize(NotEmpty constraintAnnotation) {
    }

    @Override
    public boolean isValid(String var1, ConstraintValidatorContext context) {
        if(StringUtils.isBlank(var1)){
            return false;
        }
        return true;
    }
}