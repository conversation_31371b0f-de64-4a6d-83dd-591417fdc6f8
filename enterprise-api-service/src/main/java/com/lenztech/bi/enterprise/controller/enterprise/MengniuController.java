package com.lenztech.bi.enterprise.controller.enterprise;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.service.enterprise.MengniuServiceImpl;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuBiTargetResp;
import com.trax.lenz.api.dto.enterprise.mengniu.MengniuH5BiTargetResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 蒙牛bi指标Controller
 *
 * <AUTHOR>
 * @date 2022-06-13 13:44:19
 */
@Slf4j
@RestController
@RequestMapping("/biResult/mengniu/")
public class MengniuController {

    private MengniuServiceImpl mengniuService;

    public MengniuController(MengniuServiceImpl mengniuService) {
        this.mengniuService = mengniuService;
    }

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<MengniuBiTargetResp>
     */
    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    public ResponseData<MengniuBiTargetResp> getTargetList(String responseId) {
        try {
            MengniuBiTargetResp mengniuBiTargetResp = mengniuService.getBiTarget(responseId);
            return ResponseData.success().data(mengniuBiTargetResp);
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取指标列表
     *
     * @param responseId
     * @return ResponseData<MengniuH5BiTargetResp>
     */
    @RequestMapping(value = "getH5TargetList", method = RequestMethod.GET)
    public ResponseData<MengniuH5BiTargetResp> getH5TargetList(String responseId) {
        try {
            MengniuH5BiTargetResp mengniuBiTargetResp = mengniuService.getH5BiTarget(responseId);
            return ResponseData.success().data(mengniuBiTargetResp);
        } catch (Exception e) {
            log.error("/getTargetList========", e);
        }
        return ResponseData.failure();
    }

}
