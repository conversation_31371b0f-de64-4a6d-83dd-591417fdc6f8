package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.chinashop.ChinaShopDemoDTO;
import com.lenztech.bi.enterprise.dto.chinashop.WmddQuestionMapDTO;
import com.lenztech.bi.enterprise.entity.WmddQuestionMap;
import com.lenztech.bi.enterprise.service.IChinaShopService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/11/2 13:42
 **/

@RestController
@RequestMapping("/biResult/ChinaShop")
public class ChinaShopController {

    public static final Logger logger = LoggerFactory.getLogger(ChinaShopController.class);
    @Autowired
    private IChinaShopService chinaShopService;

    @RequestMapping(value = "/getChinaShopResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取ChinaShop报表页面数据")
    public ResponseData getChinaShopResult() {
        ChinaShopDemoDTO result = chinaShopService.getChinaShopResult();
        return ResponseData.success(result);
    }

    /**
     * 获取ChinaShop报表页面数据 V2版本, 获取某个区域的数据
     * @param questionId
     * @return
     */
    @RequestMapping(value = "/getChinaShopResult/V2", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取ChinaShop报表页面数据")
    public ResponseData getChinaShopResultV2(String questionId) {
        try {
            ChinaShopDemoDTO result = chinaShopService.getChinaShopResultV2(questionId);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getChinaShopResultV2========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取某个区域列表数据
     * @return
     */
    @RequestMapping(value = "/getAreaList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取ChinaShop某个区域列表数据")
    public ResponseData getAreaList() {
        try {
            List<WmddQuestionMapDTO> result = chinaShopService.getWmddQuestionMapList();
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getAreaList========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取ChinaShop报表页面数据 V3版本
     * @return
     */
    @RequestMapping(value = "/getChinaShopResult/V3", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取ChinaShop报表页面数据 V3版本")
    public ResponseData getChinaShopResultV3(String questionId) {
        try {
            ChinaShopDemoDTO result = chinaShopService.getChinaShopResultV3(questionId);
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getChinaShopResultV3========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取某个区域列表数据 隶属于ChinaShop报表页面数据 V3版本
     * @return
     */
    @RequestMapping(value = "/getAreaList/V3", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取ChinaShop某个区域列表数据")
    public ResponseData getAreaListV3() {
        try {
            List<WmddQuestionMapDTO> result = chinaShopService.getChinaShopQuestionMapListV3();
            return ResponseData.success(result);
        } catch (Exception e) {
            logger.error("/getAreaListV3========", e);
        }
        return ResponseData.failure();
    }
}
