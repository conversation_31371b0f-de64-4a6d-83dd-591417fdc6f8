package com.lenztech.bi.enterprise.validator;

import com.lenztech.bi.enterprise.validator.annotation.NotNull;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collection;

public class NotNullValidatorImpl implements ConstraintValidator<NotNull, Collection> {


    @Override
    public void initialize(NotNull constraintAnnotation) {
    }

    @Override
    public boolean isValid(Collection collection, ConstraintValidatorContext context) {
        if(CollectionUtils.isEmpty(collection)){
            return false;
        }
        return true;
    }
}