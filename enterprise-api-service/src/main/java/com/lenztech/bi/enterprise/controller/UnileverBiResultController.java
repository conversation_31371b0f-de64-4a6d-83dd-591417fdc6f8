package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.unilever.SecondaryDisplay;
import com.lenztech.bi.enterprise.dto.wangwang.WangWangBiTargetResp;
import com.lenztech.bi.enterprise.entity.UnileverOsaDetail;
import com.lenztech.bi.enterprise.entity.UnileverOsaStatus;
import com.lenztech.bi.enterprise.entity.UnileverStoreinfo;
import com.lenztech.bi.enterprise.service.UnileverReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * Created with IntelliJ IDEA.
 * User: sun<PERSON>yuan
 * Date: 2020/12/17
 * Time: 15:05
 * 类功能: 联合利华poc相关接口
 */
@RestController
@RequestMapping("/biResult/unilever/")
public class UnileverBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(UnileverBiResultController.class);
    @Autowired
    private UnileverReportService unileverReportService;

    /**
     * 获取联合利华门店相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getUnileverStoreinfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<UnileverStoreinfo> getUnileverStoreinfo(String responseId) {
        try {
            UnileverStoreinfo unileverStoreinfo = unileverReportService.getUnileverStoreinfoByRid(responseId);
            return ResponseData.success().data(unileverStoreinfo);
        } catch (Exception e) {
            logger.error("/getUnileverStoreinfo========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取联合利华小程序识别结果
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getIsRecognitionComplete", method = RequestMethod.GET)
    @ControllerAnnotation(use = "小程序-获取联合利华小程序识别结果")
    public ResponseData<UnileverStoreinfo> getIsRecognitionComplete(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getIsRecognitionComplete(responseId));
        } catch (Exception e) {
            logger.error("/getIsRecognitionComplete========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取OSA分销数据
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getOsaDistribution", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取OSA分销")
    public ResponseData getOsaDistribution(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getOsaDistribution(responseId));
        } catch (Exception e) {
            logger.error("/getOsaDistribution========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取OSA分销数据产品明细
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getOsaDistributionProductDetail", method = RequestMethod.POST)
    @ControllerAnnotation(use = "报表-获取OSA分销产品明细")
    public ResponseData getOsaDistributionProductDetail(String responseId, String cotcName) {
        try {
            //解码前需先再次编码 否则会丢失掉字符中的+ -
//            cotcName = URLEncoder.encode(cotcName, "utf-8");
            return ResponseData.success().data(unileverReportService.getOsaDistributionProductDetail(responseId, URLDecoder.decode(cotcName, "utf-8")));
        } catch (Exception e) {
            logger.error("/getOsaDistributionProductDetail========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取TDP分销数据
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getTdpDistribution", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取TDP分销数据")
    public ResponseData getTdpDistribution(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getTdpDistribution(responseId));
        } catch (Exception e) {
            logger.error("/getTdpDistribution========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取TDP分销数据产品明细
     * @param responseId
     * @param category
     * @param brandType
     * @return
     */
    @RequestMapping(value = "getTdpDistributionProductDetail", method = RequestMethod.POST)
    @ControllerAnnotation(use = "报表-获取TDP分销数据产品明细")
    public ResponseData getTdpDistributionProductDetail(String responseId, String category, String brandType) {
        try {
//            brandType = URLEncoder.encode(brandType, "utf-8");
//            category = URLEncoder.encode(category, "utf-8");
            return ResponseData.success().data(unileverReportService.getTdpDistributionProductDetail(responseId, URLDecoder.decode(category, "utf-8"), URLDecoder.decode(brandType, "utf-8")));
        } catch (Exception e) {
            logger.error("/getTdpDistributionProductDetail========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取CTA POSM数据
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getCtaPosm", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取CTA POSM数据")
    public ResponseData getCtaPosm(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getCtaPosm(responseId));
        } catch (Exception e) {
            logger.error("/getCtaPosm========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取SOS货架品牌占比
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getSosBrandRatio", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取SOS货架品牌占比")
    public ResponseData getSosBrandRatio(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getSosBrandRatio(responseId));
        } catch (Exception e) {
            logger.error("/getSosBrandRatio========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取价签信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getPriceTag", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取价签信息")
    public ResponseData getPriceTag(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getPriceTag(responseId));
        } catch (Exception e) {
            logger.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取二次陈列
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getSecondaryDisplay", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取二次陈列")
    public ResponseData<SecondaryDisplay> getSecondaryDisplay(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getSecondaryDisplay(responseId));
        } catch (Exception e) {
            logger.error("/getSecondaryDisplay========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取二次陈列图片
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getSecondDisplayImage", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取二次陈列图片")
    public ResponseData getSecondDisplayImage(String responseId, String repeatNo) {
        try {
            return ResponseData.success().data(unileverReportService.getSecondDisplayImage(responseId, repeatNo));
        } catch (Exception e) {
            logger.error("/getSecondDisplayImage========", e);
        }
        return ResponseData.failure();
    }


    /**
     * 获取价签信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getUnileverResult", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取联合利华识别结果")
    public ResponseData getUnileverResult(String responseId) {
        try {
            return ResponseData.success().data(unileverReportService.getUnileverResult(responseId));
        } catch (Exception e) {
            logger.error("/getPriceTag========", e);
        }
        return ResponseData.failure();
    }
}
