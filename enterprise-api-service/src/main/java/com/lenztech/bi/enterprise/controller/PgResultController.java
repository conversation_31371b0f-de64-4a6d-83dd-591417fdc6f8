//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.pg.PgPocBrandFacingListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocRuleListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocSkuExistListDTO;
//import com.lenztech.bi.enterprise.dto.pg.PgPocSkuNotExistListDTO;
//import com.lenztech.bi.enterprise.entity.PgPocKpiEntity;
//import com.lenztech.bi.enterprise.service.PgEnterpriseService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * 宝洁H5演示Demo查询BI数据接口
// * <AUTHOR>
// * @version V1.0
// * @date 2019-11-07 15:15
// * @since JDK 1.8
// */
//@RestController
//@RequestMapping("/biResult/pg")
//public class PgResultController {
//
//    private PgEnterpriseService pgEnterpriseService;
//
//    @Autowired
//    public PgResultController(PgEnterpriseService pgEnterpriseService) {
//        this.pgEnterpriseService = pgEnterpriseService;
//    }
//
//    @GetMapping("/listPgPocBrandFacingEntity/{responseId}")
//    public ResponseData<PgPocBrandFacingListDTO> listPgPocBrandFacingEntity(@PathVariable("responseId") String responseId) {
//        return pgEnterpriseService.listPgPocBrandFacingEntity(responseId);
//    }
//
//    @GetMapping("/selectPgPocKpiEntityByRid/{responseId}")
//    public ResponseData<PgPocKpiEntity> selectPgPocKpiEntityByRid(@PathVariable("responseId") String responseId) {
//        return pgEnterpriseService.selectPgPocKpiEntityByRid(responseId);
//    }
//
//    @GetMapping("/listPgPocRuleEntity/{responseId}")
//    public ResponseData<PgPocRuleListDTO> listPgPocRuleEntity(@PathVariable("responseId") String responseId) {
//        return pgEnterpriseService.listPgPocRuleEntity(responseId);
//    }
//
//    @GetMapping("/listPgPocSkuExistEntity/{responseId}")
//    public ResponseData<PgPocSkuExistListDTO> listPgPocSkuExistEntity(@PathVariable("responseId") String responseId) {
//        return pgEnterpriseService.listPgPocSkuExistEntity(responseId);
//    }
//
//    @GetMapping("/listPgPocSkuNotExistEntity/{responseId}")
//    public ResponseData<PgPocSkuNotExistListDTO> listPgPocSkuNotExistEntity(@PathVariable("responseId") String responseId) {
//        return pgEnterpriseService.listPgPocSkuNotExistEntity(responseId);
//    }
//
//}
