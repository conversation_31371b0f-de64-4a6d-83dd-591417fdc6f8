package com.lenztech.bi.enterprise.controller;


import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.bi.PepsiCoBiResultResp;
import com.lenztech.bi.enterprise.service.PepsiCoBiResultService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 百事bi结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@RestController
@RequestMapping("/biResult/pepsiCo/")
public class PepsiCoBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(PepsiCoBiResultController.class);

    @Autowired
    private PepsiCoBiResultService pepsiCoBiResultService;

    /**
     * 获取百事BI 门店识别结果
     *
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取门店相关信息")
    public ResponseData<PepsiCoBiResultResp> getStoreinfo(String responseId, String taskId) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return ResponseData.failure();
            }
            PepsiCoBiResultResp storeInfo = pepsiCoBiResultService.getStoreInfo(responseId, taskId);
            return ResponseData.success(storeInfo);
        } catch (Exception e) {
            logger.error("/getStoreInfo========", e);
        }
        return ResponseData.failure();
    }

}