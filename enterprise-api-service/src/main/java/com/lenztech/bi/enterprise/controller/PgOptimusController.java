package com.lenztech.bi.enterprise.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.CustomerResponseData;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusBrandLevelDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusCategoryLevelDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusQueryParam;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusSkuLevelDisplayDTO;
import com.lenztech.bi.enterprise.dto.pg.PgOptimusSkuLevelShelfDTO;
import com.lenztech.bi.enterprise.service.PgOptimusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RestController
@RequestMapping("/biResult/pgOptimus/")
public class PgOptimusController {

    ExecutorService executorService = Executors.newFixedThreadPool(1);
    public static final Logger logger = LoggerFactory.getLogger(PgOptimusController.class);
    @Autowired
    PgOptimusService pgOptimusService;

    /**
     * 获取Brand级别二陈数据表（分页）
     * @param param 查询参数
     * @return 品牌级别二陈数据列表（分页）
     */
    @RequestMapping(value = "getBrandLevelDisplayBiResult", method = RequestMethod.POST)
    @ControllerAnnotation(use = "获取Brand级别二陈数据表")
    public CustomerResponseData<IPage<PgOptimusBrandLevelDisplayDTO>> getBrandLevelDisplayBiResult(
            @RequestBody PgOptimusQueryParam param) {
        try {
            IPage<PgOptimusBrandLevelDisplayDTO> result = pgOptimusService.getBrandLevelDisplayBiResult(
                    param.getSearchDate(), param.getResponseId(), param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("获取Brand级别二陈数据表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据responseId获取Brand级别二陈数据
     * @param responseId 答卷ID
     * @return 品牌级别二陈数据列表
     */
    @RequestMapping(value = "getBrandLevelDisplayByResponseId", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据responseId获取Brand级别二陈数据")
    public CustomerResponseData<List<PgOptimusBrandLevelDisplayDTO>> getBrandLevelDisplayByResponseId(
            @RequestParam("responseId") String responseId) {
        try {
            List<PgOptimusBrandLevelDisplayDTO> result = pgOptimusService.getBrandLevelDisplayByResponseId(responseId);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据responseId获取Brand级别二陈数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 获取Category级别二陈数据表（分页）
     * @param param 查询参数
     * @return 品类级别二陈数据列表（分页）
     */
    @RequestMapping(value = "getCategoryLevelDisplayBiResult", method = RequestMethod.POST)
    @ControllerAnnotation(use = "获取Category级别二陈数据表")
    public CustomerResponseData<IPage<PgOptimusCategoryLevelDisplayDTO>> getCategoryLevelDisplayBiResult(
            @RequestBody PgOptimusQueryParam param) {
        try {
            IPage<PgOptimusCategoryLevelDisplayDTO> result = pgOptimusService.getCategoryLevelDisplayBiResult(
                    param.getSearchDate(), param.getResponseId(), param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("获取Category级别二陈数据表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据responseId获取Category级别二陈数据
     * @param responseId 答卷ID
     * @return 品类级别二陈数据列表
     */
    @RequestMapping(value = "getCategoryLevelDisplayByResponseId", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据responseId获取Category级别二陈数据")
    public CustomerResponseData<List<PgOptimusCategoryLevelDisplayDTO>> getCategoryLevelDisplayByResponseId(
            @RequestParam("responseId") String responseId) {
        try {
            List<PgOptimusCategoryLevelDisplayDTO> result = pgOptimusService.getCategoryLevelDisplayByResponseId(responseId);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据responseId获取Category级别二陈数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 获取SKU级别二陈数据表（分页）
     * @param param 查询参数
     * @return SKU级别二陈数据列表（分页）
     */
    @RequestMapping(value = "getSkuLevelDisplayBiResult", method = RequestMethod.POST)
    @ControllerAnnotation(use = "获取SKU级别二陈数据表")
    public CustomerResponseData<IPage<PgOptimusSkuLevelDisplayDTO>> getSkuLevelDisplayBiResult(
            @RequestBody PgOptimusQueryParam param) {
        try {
            IPage<PgOptimusSkuLevelDisplayDTO> result = pgOptimusService.getSkuLevelDisplayBiResult(
                    param.getSearchDate(), param.getResponseId(), param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("获取SKU级别二陈数据表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据responseId获取SKU级别二陈数据
     * @param responseId 答卷ID
     * @return SKU级别二陈数据列表
     */
    @RequestMapping(value = "getSkuLevelDisplayByResponseId", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据responseId获取SKU级别二陈数据")
    public CustomerResponseData<List<PgOptimusSkuLevelDisplayDTO>> getSkuLevelDisplayByResponseId(
            @RequestParam("responseId") String responseId) {
        try {
            List<PgOptimusSkuLevelDisplayDTO> result = pgOptimusService.getSkuLevelDisplayByResponseId(responseId);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据responseId获取SKU级别二陈数据异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 获取SKU级别货架数据表（分页）
     * @param param 查询参数
     * @return SKU级别货架数据列表（分页）
     */
    @RequestMapping(value = "getSkuLevelShelfBiResult", method = RequestMethod.POST)
    @ControllerAnnotation(use = "获取SKU级别货架数据表")
    public CustomerResponseData<IPage<PgOptimusSkuLevelShelfDTO>> getSkuLevelShelfBiResult(
            @RequestBody PgOptimusQueryParam param) {
        try {
            IPage<PgOptimusSkuLevelShelfDTO> result = pgOptimusService.getSkuLevelShelfBiResult(
                    param.getSearchDate(), param.getResponseId(), param.getPageNum(), param.getPageSize());
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("获取SKU级别货架数据表异常", e);
            return CustomerResponseData.failure();
        }
    }

    /**
     * 根据responseId获取SKU级别货架数据
     * @param responseId 答卷ID
     * @return SKU级别货架数据列表
     */
    @RequestMapping(value = "getSkuLevelShelfByResponseId", method = RequestMethod.GET)
    @ControllerAnnotation(use = "根据responseId获取SKU级别货架数据")
    public CustomerResponseData<List<PgOptimusSkuLevelShelfDTO>> getSkuLevelShelfByResponseId(
            @RequestParam("responseId") String responseId) {
        try {
            List<PgOptimusSkuLevelShelfDTO> result = pgOptimusService.getSkuLevelShelfByResponseId(responseId);
            return CustomerResponseData.success(result);
        } catch (Exception e) {
            logger.error("根据responseId获取SKU级别货架数据异常", e);
            return CustomerResponseData.failure();
        }
    }
}
