package com.lenztech.bi.enterprise.controller.system;

import com.lenztech.bi.enterprise.service.AdminRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("apidoc")
public class ApiDocController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AdminRoleService adminRoleService;

    /**
     * 跳转到角色管理
     * @return
     */
    @RequestMapping
    public String toPage() {
        return "/apidoc/apidocManage";
    }

}
