package com.lenztech.bi.enterprise.executor;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 线程池
 * @Author: z<PERSON><PERSON>e
 * @Date: 25/9/18 PM6:21
 */
public class AppThreadExecutor implements Executor {

	/**
	 * 执行器实例
	 */
    private static volatile Executor mExecutor;

    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();


    private static final int                     CORE_POOL_SIZE  = Math.max(2, Math.min(CPU_COUNT - 1, 4));;
    private static final int                     MAX_POOL_SIZE   = 20;
    private static final int                     KEEP_ALIVE_TIME = 120;
    private static final TimeUnit                TIME_UNIT       = TimeUnit.SECONDS;
    private static final BlockingQueue<Runnable> WORK_QUEUE      = new LinkedBlockingQueue<Runnable>();

    private static ThreadPoolExecutor mThreadPoolExecutor;

    private AppThreadExecutor() {
        long keepAlive = KEEP_ALIVE_TIME;
        mThreadPoolExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                keepAlive,
                TIME_UNIT,
                WORK_QUEUE);
    }

    @Override
    public void execute(final Runnable runnable) {
        mThreadPoolExecutor.submit(runnable);
    }

    public static Executor getInstance() {
        if (mExecutor == null) {
            mExecutor = new AppThreadExecutor();
        }
        return mExecutor;
    }

}
