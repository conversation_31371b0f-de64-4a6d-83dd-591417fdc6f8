//package com.lenztech.bi.enterprise.controller;
//
//import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
//import com.lenztech.bi.enterprise.dto.RequestData;
//import com.lenztech.bi.enterprise.dto.ResponseData;
//import com.lenztech.bi.enterprise.dto.bi.BiReportDetailReq;
//import com.lenztech.bi.enterprise.service.ArgusCompanyService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.io.IOException;
//
///**
// * @program: bi-service
// * @description: Argus 关于获取企业信息的 Controller
// *              具体原因：对于该项目的具体业务划分尚未明确（对于只负责BI统计业务，还是负责对外的API接口方面的业务划分），
// *                      最终由张建决定暂时不对功能进行迁移，
// *                      待对业务进行明确划分后，再酌情进行调整。
// *              ---- 杨涛 2020.09.23
// * @author: longgang
// * @create: 2020-09-10 14:20
// **/
//@RestController
//@RequestMapping("/argus")
//public class ArgusCompanyController {
//
//    public static final Logger LOGGER = LoggerFactory.getLogger(ArgusCompanyController.class);
//
//    @Autowired
//    private ArgusCompanyService argusCompanyService ;
//
//    @RequestMapping(value = "/getCompanyMessage", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "获取BI公司信息")
//    public ResponseData getCompanyMessage(@RequestParam("companyName") String companyName) {
//
//        return argusCompanyService.getCompanyMessage(companyName);
//    }
//
//    @RequestMapping(value = "/getCompanyId", method = RequestMethod.GET)
//    @ControllerAnnotation(use = "获取BI公司信息")
//    public ResponseData getCompanyId(@RequestParam("companyName") String companyName) {
//
//        return argusCompanyService.getCompanyId(companyName);
//    }
//
//
//}
