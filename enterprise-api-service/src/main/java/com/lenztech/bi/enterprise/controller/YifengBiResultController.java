package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.bnhd.ProductPriceInfo;
import com.lenztech.bi.enterprise.dto.bnhd.RecognizeSceneInfo;
import com.lenztech.bi.enterprise.entity.BnhdPosmsResult;
import com.lenztech.bi.enterprise.entity.BnhdStoreRecord;
import com.lenztech.bi.enterprise.entity.YifengpharmStoreRecord;
import com.lenztech.bi.enterprise.service.BnhdReportService;
import com.lenztech.bi.enterprise.service.YifengReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/07/19
 * Time: 17:19
 * 类功能: 益丰药房poc相关接口
 */
@RestController
@RequestMapping("/biResult/yifeng/")
public class YifengBiResultController {

    public static final Logger logger = LoggerFactory.getLogger(YifengBiResultController.class);

    @Autowired
    private YifengReportService yifengReportService;

    /**
     * 获取益丰药房门店相关信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getStoreinfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取门店相关信息")
    public ResponseData<YifengpharmStoreRecord> getStoreinfo(String responseId) {
        try {
            YifengpharmStoreRecord yifengpharmStoreRecord = yifengReportService.getStoreinfo(responseId);
            return ResponseData.success().data(yifengpharmStoreRecord);
        } catch (Exception e) {
            logger.error("/getStoreinfo========", e);
        }
        return ResponseData.failure();
    }

    /**
     * 获取益丰药房产品信息
     * @param responseId
     * @return
     */
    @RequestMapping(value = "getRecognizeProductsInfo", method = RequestMethod.GET)
    @ControllerAnnotation(use = "报表-获取益丰药房产品信息")
    public ResponseData getRecognizeProductsInfo(String responseId) {
        try {
            List<ProductPriceInfo> productPriceInfoList = yifengReportService.getRecognizeProductsInfo(responseId);
            return ResponseData.success().data(productPriceInfoList);
        } catch (Exception e) {
            logger.error("/getRecognizeProductsInfo========", e);
        }
        return ResponseData.failure();
    }


}
