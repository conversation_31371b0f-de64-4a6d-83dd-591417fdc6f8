package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation;
import com.lenztech.bi.enterprise.dto.BIResultRet;
import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.jzv2.JzV2BiTargetResp;
import com.lenztech.bi.enterprise.dto.libyV2.RecognizeDetailResp;
import com.lenztech.bi.enterprise.service.JzV2ReportService;
import com.lenztech.bi.enterprise.service.LiByEnterpriseService;
import com.lenztech.bi.enterprise.service.LiByV2ReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 江中二期获取BI结果
 *
 * <AUTHOR>
 * @date 2021-10-11 16:05
 */
@RestController
@RequestMapping("/biResult/libai/")
public class LiByV2BiResultController {

    public static final Logger logger = LoggerFactory.getLogger(LiByV2BiResultController.class);

    @Autowired
    private LiByV2ReportService liByV2ReportService;

    @RequestMapping(value = "getTargetList", method = RequestMethod.GET)
    @ControllerAnnotation(use = "获取立白二期识别结果")
    public ResponseData<BIResultRet> getBiTargetList(@RequestParam("responseId") String responseId) {
        try {
            RecognizeDetailResp biTargetList = liByV2ReportService.getBiTargetList(responseId);
            return ResponseData.success().data(biTargetList);
        } catch (Exception e) {
            logger.info("【立白二期】查询bi结果集异常！", e);
        }
        return ResponseData.failure();
    }

}
