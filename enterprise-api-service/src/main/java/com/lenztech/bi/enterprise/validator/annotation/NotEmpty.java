package com.lenztech.bi.enterprise.validator.annotation;

import com.lenztech.bi.enterprise.validator.NotNullArrayValidatorImpl;
import com.lenztech.bi.enterprise.validator.NotNullValidatorImpl;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {
        NotNullValidatorImpl.class,
        NotNullArrayValidatorImpl.class
})
public @interface NotEmpty {

    String message() default "must not be empty";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
