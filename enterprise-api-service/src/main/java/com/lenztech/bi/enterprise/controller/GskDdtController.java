package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.ResponseData;
import com.lenztech.bi.enterprise.dto.gsk.GskPosmDetailDto;
import com.lenztech.bi.enterprise.dto.gsk.GskPosmDetailReq;
import com.lenztech.bi.enterprise.dto.gsk.GskPosmDto;
import com.lenztech.bi.enterprise.dto.gsk.GskPosmReq;
import com.lenztech.bi.enterprise.dto.unileverapp.ApiResultDTO;
import com.lenztech.bi.enterprise.service.impl.GskDdtAndDdrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> lv
 */
@Slf4j
@RestController
@RequestMapping("/bi/gsk/report")
public class GskDdtController {


    @Autowired
    private GskDdtAndDdrService ddtAndDdrService;

    /**
     * posm 动态列表
     *
     * @param gskPosmReq
     * @return
     */
    @RequestMapping(value = "getPosmList", method = RequestMethod.POST)
    public ResponseData<List<GskPosmDto>> getPosmList(@RequestBody GskPosmReq gskPosmReq) {
        log.info("【Posm动态列表请求】, gskPosmReq:{}", gskPosmReq);
        try {
            return ResponseData.success().data(ddtAndDdrService.getPosmList(gskPosmReq));
        } catch (Exception e) {
            log.error("【Posm动态列表请求!】", e);
        }
        return ResponseData.failure();
    }

    /**
     * posm 动态列表
     *
     * @param gskPosmDetailReq
     * @return
     */
    @RequestMapping(value = "getPosmDetail", method = RequestMethod.POST)
    public ResponseData<List<GskPosmDetailDto>> getPosmDetail(@RequestBody GskPosmDetailReq gskPosmDetailReq) {
        log.info("【Posm动态详情请求】, gskPosmDetailReq:{}", gskPosmDetailReq);
        try {
            return ResponseData.success().data(ddtAndDdrService.getPosmDetail(gskPosmDetailReq));
        } catch (Exception e) {
            log.error("【Posm动态详情请求!】", e);
        }
        return ResponseData.failure();
    }

    /**
     * 常规问卷列表
     *
     * @param gskPosmReq
     * @return
     */
    @RequestMapping(value = "getProductList", method = RequestMethod.POST)
    public ResponseData<List<GskPosmDto>> getProductList(@RequestBody GskPosmReq gskPosmReq) {
        log.info("【常规列表请求】, gskPosmReq:{}", gskPosmReq);
        try {
            return ResponseData.success().data(ddtAndDdrService.getProductList(gskPosmReq));
        } catch (Exception e) {
            log.error("【常规列表请求!】", e);
        }
        return ResponseData.failure();
    }

    /**
     * 常规问卷详情
     *
     * @param gskPosmDetailReq
     * @return
     */
    @RequestMapping(value = "getProductDetail", method = RequestMethod.POST)
    public ResponseData<List<GskPosmDetailDto>> getProductDetail(@RequestBody GskPosmDetailReq gskPosmDetailReq) {
        log.info("【常规详情请求】, gskPosmDetailReq:{}", gskPosmDetailReq);
        try {
            return ResponseData.success().data(ddtAndDdrService.getProductDetail(gskPosmDetailReq));
        } catch (Exception e) {
            log.error("【常规详情请求!】", e);
        }
        return ResponseData.failure();
    }


    /**
     * 动态问卷时间下拉
     *
     * @param gskPosmReq
     * @return
     */
    @RequestMapping(value = "getUpTimePosmList", method = RequestMethod.POST)
    public ResponseData<List<String>> getUpTimePosmList(@RequestBody GskPosmReq gskPosmReq) {
        log.info("【动态问卷时间下拉请求】, gskPosmDetailReq:{}", gskPosmReq);
        try {
            return ResponseData.success().data(ddtAndDdrService.getUpTimePosmList(gskPosmReq));
        } catch (Exception e) {
            log.error("【动态问卷时间下拉请求!】", e);
        }
        return ResponseData.failure();
    }


    /**
     * 常规问卷时间下拉
     *
     * @param gskPosmReq
     * @return
     */
    @RequestMapping(value = "getUpTimeProductList", method = RequestMethod.POST)
    public ResponseData<List<String>> getUpTimeProductList(@RequestBody GskPosmReq gskPosmReq) {
        log.info("【动态问卷时间下拉请求】, gskPosmDetailReq:{}", gskPosmReq);
        try {
            return ResponseData.success().data(ddtAndDdrService.getUpTimeProductList(gskPosmReq));
        } catch (Exception e) {
            log.error("【动态问卷时间下拉请求!】", e);
        }
        return ResponseData.failure();
    }

}
