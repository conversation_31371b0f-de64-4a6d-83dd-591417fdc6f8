package com.lenztech.bi.enterprise.controller.aspect;

import com.alibaba.druid.support.http.WebStatFilter;
import com.lenztech.bi.enterprise.comon.Constant;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import com.lenztech.bi.enterprise.utils.StringUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

@Aspect
@Order(0)
@Component
public class ControllerAnnotationAspect {

    private static final Logger loggerAspect = LoggerFactory.getLogger(ControllerAnnotationAspect.class);

    private static Logger logger = loggerAspect;

    private static final String DATE_FORMAT = "yyyyMMddHHmmssSSS";
    private static ThreadLocal<DateFormat> threadLocal = new ThreadLocal<DateFormat>();

    public static DateFormat getDateFormat() {
        DateFormat df = threadLocal.get();
        if (df == null) {
            df = new SimpleDateFormat(DATE_FORMAT);
            threadLocal.set(df);
        }
        return df;
    }

    @Pointcut("@annotation(com.lenztech.bi.enterprise.controller.aspect.ControllerAnnotation)")
    public void annotationAspect() {
    }

    /**
     * 方法说明：切片处理，获取自定义日志输出前缀
     *
     * @param joinPoint
     * @param annotation
     * @return
     * @throws Throwable
     */
    @Around("annotationAspect() && @annotation(annotation)")
    public Object doAspect(ProceedingJoinPoint joinPoint, ControllerAnnotation annotation) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object response = null;
        String use = "";
        String method = "";
        StringBuffer sbLog = new StringBuffer();
        try {
            Class<?> cls = joinPoint.getTarget().getClass();

            if (LoggerFactory.getLogger(cls) != null) {
                logger = LoggerFactory.getLogger(cls);
            }
            // 获得外部传入的接口用途描述，加入到日志打印中
            use = annotation.use();

            Signature signature = joinPoint.getSignature();
            MethodSignature methodSignature = (MethodSignature) signature;
            Method targetMethod = methodSignature.getMethod();


            // http请求通过这里的方法可以获取
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

            method = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();

            // 设置日志记录traceId逻辑 如果需要有入参，这里的方法自行修改
            AspectMdcUtil.beginTrackIdMdc(request, method, getDateFormat());
            // 请求路径
            AspectMdcUtil.beginUrlPathMdc(cls, targetMethod);

            // 参数
            Object[] args = joinPoint.getArgs();

            if (args != null) {
                for (Object arg : args) {
                    if (arg != null) {
                        try {
                            String param = null;
                            if (arg instanceof String) {
                                param = StringUtil.replaceBlank(arg.toString());
                            } else if (arg instanceof WebStatFilter.StatHttpServletResponseWrapper) {
                                param = Constant.VALUE_BLANK;
                            } else {
                                param = StringUtil.replaceBlank(JsonUtil.toJsonString(arg));
                            }
                            sbLog.append("请求参数:").append(param);
                            sbLog.append(Constant.VALUE_NEWLINE);
                        } catch (Exception ex) {
                            logger.error("doAspect args", ex);
                        }
                    }
                }
            } else {
                sbLog.append("请求参数: 无");
            }
            // 执行controller方法体
            response = joinPoint.proceed(args);
            // 记录最终返回结果
            sbLog.append(use).append(" 结果: ").append(JsonUtil.toJsonString(response));
            sbLog.append(Constant.VALUE_NEWLINE);

            long time = System.currentTimeMillis() - startTime;
            sbLog.append(use).append(" 接口耗时: ").append(time).append("ms");
            sbLog.append(Constant.VALUE_NEWLINE);

            // 消耗时间
            AspectMdcUtil.beginConsumeTimeMdc(time);
            logger.info(sbLog.toString());

            return response;
        } finally {
            MDC.clear();
            threadLocal.remove();
        }
    }
}
