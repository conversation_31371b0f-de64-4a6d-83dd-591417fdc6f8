package com.lenztech.bi.enterprise.service;

import com.lenztech.bi.enterprise.dto.SnowBeerCompleteStatusResp;
import com.lenztech.bi.enterprise.dto.hipac.HiPacBiTargetResp;
import com.lenztech.bi.enterprise.dto.hipac.HipacCompleteStatusResp;

/**
 * 海拍客bi指标Service
 *
 * <AUTHOR>
 * @date 2021-05-24 13:16:20
 */
public interface HiPacReportService {

    /**
     * 根据答案id查询bi识别指标结果
     *
     * @param responseId 答案ID
     * @return HiPacBiTargetResp
     */
    HiPacBiTargetResp getBiTargetList(String responseId);

    /**
     * 根据答案id查询数据处理状态
     *
     * @param responseId 答案ID
     */
    HipacCompleteStatusResp getCompleteStatus(String responseId);

}
