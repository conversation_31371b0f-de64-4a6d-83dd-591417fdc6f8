# PG RTIR 数据同步定时器配置示例

## 概述

本文档提供了配置定时器来调用PG RTIR数据同步接口的示例。

## 接口信息

- **接口地址**: `/biResult/rtir/syncDataFromApi`
- **请求方法**: GET
- **参数**: 无需参数
- **功能**: 自动分页获取第三方API的所有数据并同步到数据库

## 配置方式

### 1. Linux Crontab配置

#### 每天凌晨2点执行
```bash
# 编辑crontab
crontab -e

# 添加以下配置
0 2 * * * curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi" >> /var/log/pg_rtir_sync.log 2>&1
```

#### 每天凌晨3点执行（带超时控制）
```bash
0 3 * * * timeout 3600 curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi" >> /var/log/pg_rtir_sync.log 2>&1
```

#### 每周一凌晨2点执行
```bash
0 2 * * 1 curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi" >> /var/log/pg_rtir_sync.log 2>&1
```

### 2. Spring Boot @Scheduled注解配置

如果希望在应用内部配置定时任务，可以创建一个定时任务类：

```java
@Component
@Slf4j
public class PgRtirSyncScheduler {

    @Autowired
    private PgRtirService pgRtirService;

    /**
     * 每天凌晨2点执行数据同步
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncPgRtirData() {
        try {
            log.info("开始执行定时同步PG RTIR数据");
            String result = pgRtirService.syncAllDataFromThirdPartyApi();
            log.info("定时同步完成: {}", result);
        } catch (Exception e) {
            log.error("定时同步PG RTIR数据失败", e);
        }
    }
}
```

### 3. 使用脚本调用

#### Bash脚本示例
```bash
#!/bin/bash

# pg_rtir_sync.sh
# PG RTIR数据同步脚本

LOG_FILE="/var/log/pg_rtir_sync.log"
API_URL="http://localhost:8080/biResult/rtir/syncDataFromApi"

echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始同步PG RTIR数据" >> $LOG_FILE

# 调用API
response=$(curl -s -X GET "$API_URL")
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 同步成功: $response" >> $LOG_FILE
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 同步失败，退出码: $exit_code" >> $LOG_FILE
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - 同步结束" >> $LOG_FILE
```

#### 使脚本可执行并配置crontab
```bash
# 使脚本可执行
chmod +x /path/to/pg_rtir_sync.sh

# 配置crontab
0 2 * * * /path/to/pg_rtir_sync.sh
```

### 4. Python脚本示例

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    filename='/var/log/pg_rtir_sync.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def sync_pg_rtir_data():
    """同步PG RTIR数据"""
    api_url = "http://localhost:8080/biResult/rtir/syncDataFromApi"
    
    try:
        logging.info("开始同步PG RTIR数据")
        
        response = requests.get(api_url, timeout=3600)  # 1小时超时
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                logging.info(f"同步成功: {result.get('data')}")
            else:
                logging.error(f"同步失败: {result.get('msg')}")
        else:
            logging.error(f"HTTP请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.Timeout:
        logging.error("请求超时")
    except requests.exceptions.RequestException as e:
        logging.error(f"请求异常: {e}")
    except Exception as e:
        logging.error(f"未知异常: {e}")

if __name__ == "__main__":
    sync_pg_rtir_data()
```

## 监控和告警

### 1. 日志监控
```bash
# 查看同步日志
tail -f /var/log/pg_rtir_sync.log

# 查看最近的同步结果
grep "$(date '+%Y-%m-%d')" /var/log/pg_rtir_sync.log
```

### 2. 健康检查脚本
```bash
#!/bin/bash

# 检查今天是否有成功的同步记录
today=$(date '+%Y-%m-%d')
success_count=$(grep "$today.*同步成功" /var/log/pg_rtir_sync.log | wc -l)

if [ $success_count -eq 0 ]; then
    echo "警告：今天没有成功的同步记录"
    # 这里可以添加发送告警邮件或短信的逻辑
    exit 1
else
    echo "今天有 $success_count 次成功同步"
    exit 0
fi
```

## 注意事项

1. **执行时间**: 建议在业务低峰期执行，避免影响正常业务
2. **超时设置**: 建议设置合理的超时时间，避免长时间占用资源
3. **日志管理**: 定期清理日志文件，避免磁盘空间不足
4. **错误处理**: 配置告警机制，及时发现同步异常
5. **网络环境**: 确保服务器能够访问第三方API地址
6. **权限控制**: 确保定时任务有足够的权限访问日志文件和执行脚本

## 故障排查

### 常见问题
1. **网络连接失败**: 检查网络连接和防火墙设置
2. **API响应异常**: 检查第三方API服务状态
3. **数据库连接失败**: 检查数据库服务状态和连接配置
4. **内存不足**: 监控服务器资源使用情况

### 排查命令
```bash
# 检查服务状态
curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi"

# 检查日志
tail -100 /var/log/pg_rtir_sync.log

# 检查系统资源
top
df -h
```
