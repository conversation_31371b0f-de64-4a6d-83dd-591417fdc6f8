# PG RTIR 第三方API同步接口文档

## 接口概述

该接口用于定时从第三方API获取所有PG产品数据并同步到本地数据库的t_pg_rtir_md表中。接口会自动分页获取所有数据，无需传递参数。

## 接口信息

- **接口路径**: `/biResult/rtir/syncDataFromApi`
- **请求方法**: `GET`
- **用途**: 定时器调用，每天凌晨自动同步数据

## 请求参数

无需传递参数，接口内部会自动分页获取所有数据。

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "success": true,
    "data": "定时同步完成，共处理15页数据，获取750条记录，成功保存750条",
    "msg": "操作成功"
}
```

### 失败响应

```json
{
    "code": 500,
    "success": false,
    "data": null,
    "msg": "同步数据失败：网络连接超时"
}
```

## 同步机制

### 自动分页
- 接口内部自动分页获取数据，每页50条记录
- 从第1页开始，逐页获取直到没有更多数据
- 当某页返回的数据量小于每页大小时，认为已获取完所有数据

### 请求控制
- 每次API调用之间间隔1秒，避免对第三方服务造成压力
- 如果某页请求失败，会停止后续页面的获取

### 数据处理
- 支持新增和更新操作，基于GTIN编码判断
- 更新时保持原创建时间，更新修改时间
- 整个同步过程在事务中执行，异常时自动回滚

## 第三方API信息

- **API地址**: `https://qa-internal-api-b2b.cn-pgcloud.com/api/ghr-image-recognition/v1/gh/optimus/fpc/gtin/data/page`
- **请求方法**: `POST`
- **数据格式**: JSON
- **鉴权方式**: API签名认证

### 鉴权参数

接口调用时会自动添加以下鉴权参数：

| 参数名 | 说明 | 示例值 |
|--------|------|--------|
| api_key | API密钥 | trax-image-recognition-91582 |
| nonce_str | 随机字符串 | 由SnowFlake算法生成 |
| timestamp | 时间戳 | 2025-01-15 10:30:45 |
| sign | 签名 | 通过ApimSignUtil.sign()生成 |

### 请求头

| 请求头名称 | 值 |
|-----------|---|
| Ocp-Apim-Subscription-Key | 7810a82cceea46f395e1f76dd2e65642 |
| Content-Type | application/json |

### 第三方API响应示例

```json
{
    "code": 200,
    "success": true,
    "data": {
        "current": 1,
        "pageSize": 10,
        "size": 10,
        "total": 10816,
        "records": [
            {
                "gtinCode": "06903148047750",
                "fpcCode": "82204141",
                "itemStatus": "Inactive",
                "year": 2019,
                "sosDate": "2013-12-28",
                "categoryCode": "206000008",
                "categoryCn": "个人清洁",
                "productFormCode": "209000084",
                "productFormCn": "液体沐浴露",
                "brandCode": "205000026",
                "brandCn": "舒肤佳",
                "variantCode": "207000173",
                "variantCn": "健康柔肤",
                "productNameCn": "舒肤佳芦荟水润呵护型沐浴露1升",
                "length": 11.8,
                "width": 7.3,
                "height": 25.6,
                "fpcImageUrls": [
                    "https://cdn-data-platform.pg.com.cn/product-image/Product-Image-Details/d2e8268398cc467fa2821b9e8ef56173.jpg"
                ]
            }
        ]
    },
    "msg": "操作成功"
}
```

## 数据库表结构

数据将保存到`t_pg_rtir_md`表中，主要字段包括：

- `gtin_code`: GTIN编码（主键）
- `fpc_code`: FPC编码
- `item_status`: 商品状态
- `year`: 年份
- `sos_date`: SOS日期
- `category_code`: 品类编码
- `category_cn`: 品类名称
- `product_form_code`: 商品形式编码
- `product_form_cn`: 商品形式名称
- `brand_code`: 品牌编码
- `brand_cn`: 品牌名称
- `variant_code`: 系列编码
- `variant_cn`: 系列名称
- `product_name_cn`: 商品名称
- `length`: 长度
- `width`: 宽度
- `height`: 高度
- `fpc_image_urls`: FPC图片URL列表（JSON格式）
- `is_pg_product`: 是否本品（默认0-竞品）
- `modeling`: 是否建模（默认0-未建模）
- `is_posm`: 是否POSM（默认0-否）
- `is_trax_create`: 是否Trax新建（默认0-否）

## 使用示例

### cURL示例

```bash
curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi"
```

### JavaScript示例

```javascript
fetch('/biResult/rtir/syncDataFromApi', {
    method: 'GET'
})
.then(response => response.json())
.then(data => {
    console.log('同步结果:', data);
});
```

### 定时器配置示例

```bash
# 每天凌晨2点执行同步
0 2 * * * curl -X GET "http://localhost:8080/biResult/rtir/syncDataFromApi"
```

## 注意事项

1. **数据覆盖**: 如果数据库中已存在相同GTIN编码的记录，将会更新该记录
2. **事务处理**: 整个同步过程在事务中执行，如果出现异常会回滚
3. **日志记录**: 所有操作都会记录详细日志，便于问题排查
4. **网络控制**: 每次API调用间隔1秒，避免对第三方服务造成压力
5. **错误处理**: 网络异常、数据解析异常等都会有相应的错误提示
6. **执行时间**: 建议在业务低峰期（如凌晨）执行，避免影响正常业务
7. **监控告警**: 建议配置监控，及时发现同步异常

## 数据库准备

在使用接口前，需要确保数据库表中包含`fpc_image_urls`字段：

```sql
ALTER TABLE t_pg_rtir_md 
ADD COLUMN fpc_image_urls TEXT COMMENT 'FPC图片URL列表（JSON格式存储）';
```
